<template>
  <div id="app">
    <!-- 导航栏 -->
    <nav class="navbar" v-if="$route.meta.requiresAuth && isLoggedIn">
      <div class="nav-container">
        <div class="nav-brand">
          <router-link to="/" class="brand-link">
            <h2>{{ $t('navigation.brand') }}</h2>
          </router-link>
        </div>
        <div class="nav-links">
          <router-link to="/dashboard" class="nav-link">{{ $t('navigation.dashboard') }}</router-link>
          <router-link to="/modules" class="nav-link">{{ $t('navigation.courses') }}</router-link>
          <!-- 管理员后台链接 (仅管理员可见) -->
          <router-link v-if="isAdmin" to="/admin/dashboard" class="nav-link admin-link">
            {{ $t('navigation.admin') }}
          </router-link>
          <LanguageSwitcher />
          <button @click="logout" class="logout-btn">{{ $t('navigation.logout') }}</button>
        </div>
      </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="main-content" :class="{ 'with-nav': $route.meta.requiresAuth && isLoggedIn }">
      <router-view />
    </main>

    <!-- 全局加载指示器 -->
    <div v-if="loading" class="loading-overlay">
      <div class="spinner"></div>
      <p>{{ $t('common.loading') }}</p>
    </div>

    <!-- 全局通知 -->
    <div v-if="notification.show" class="notification" :class="notification.type">
      {{ notification.message }}
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import LanguageSwitcher from './components/LanguageSwitcher.vue'
import { updateTitleByRoute } from './utils/titleManager.js'

export default {
  name: 'App',
  components: {
    LanguageSwitcher
  },
  setup() {
    const router = useRouter()
    const route = useRoute()
    const { t } = useI18n()
    const loading = ref(false)
    const notification = ref({
      show: false,
      message: '',
      type: 'info'
    })

    const isLoggedIn = computed(() => {
      return !!localStorage.getItem('authToken')
    })

    const isAdmin = computed(() => {
      const userData = localStorage.getItem('user')
      if (!userData) return false
      try {
        const user = JSON.parse(userData)
        return user.role === 'admin'
      } catch {
        return false
      }
    })

    const logout = () => {
      localStorage.removeItem('authToken')
      localStorage.removeItem('userData')
      router.push('/login')
      showNotification(t('auth.logoutSuccess'), 'success')
    }

    const showNotification = (message, type = 'info') => {
      notification.value = {
        show: true,
        message,
        type
      }
      setTimeout(() => {
        notification.value.show = false
      }, 3000)
    }

    // 初始化页面标题
    onMounted(() => {
      updateTitleByRoute(route.name)
    })

    // 全局方法，供其他组件使用
    window.showNotification = showNotification
    window.setLoading = (state) => { loading.value = state }

    return {
      loading,
      notification,
      isLoggedIn,
      isAdmin,
      logout
    }
  }
}
</script>