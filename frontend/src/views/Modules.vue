<template>
  <div class="modules-container">
    <div class="modules-header">
      <h1>{{ $t('modules.title') }}</h1>
      <p>{{ $t('modules.subtitle') }}</p>
    </div>

    <div class="modules-grid" v-if="modules.length">
      <div 
        v-for="module in modules" 
        :key="module.module_id"
        class="module-card"
      >
        <div class="module-header">
          <h2>{{ module.title }}</h2>
          <div class="module-progress">
            <div class="progress-info">
              {{ getCompletedLessons(module) }} / {{ module.lessons.length }} {{ $t('modules.lessonsCompleted') }}
            </div>
            <div class="progress-bar">
              <div 
                class="progress-fill" 
                :style="{ width: `${getModuleProgress(module)}%` }"
              ></div>
            </div>
          </div>
        </div>
        
        <p class="module-description">{{ module.description }}</p>
        
        <div class="lessons-list">
          <div 
            v-for="lesson in module.lessons" 
            :key="lesson.lesson_id"
            class="lesson-item"
            :class="getLessonStatusClass(lesson)"
            @click="handleLessonClick(lesson)"
          >
            <div class="lesson-status">
              {{ getLessonIcon(lesson) }}
            </div>
            <div class="lesson-content">
              <div class="lesson-title">{{ lesson.title }}</div>
              <div class="lesson-meta">
                <span v-if="lesson.progress">
                  {{ getProgressText(lesson.progress) }}
                </span>
                <span v-if="!lesson.is_unlocked" class="locked-text">
                  {{ $t('modules.prerequisiteRequired') }}
                </span>
              </div>
            </div>
            <div class="lesson-arrow" v-if="lesson.is_unlocked">→</div>
          </div>
        </div>
      </div>
    </div>

    <div v-else-if="loading" class="loading-message">
      <div class="spinner"></div>
      <p>{{ $t('modules.loading') }}</p>
    </div>

    <div v-else class="empty-state">
      <h3>{{ $t('modules.emptyTitle') }}</h3>
      <p>{{ $t('modules.emptyDescription') }}</p>
      <button @click="loadModules" class="btn btn-primary">{{ $t('modules.reload') }}</button>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { contentAPI } from '../api.js'

export default {
  name: 'Modules',
  setup() {
    const { t } = useI18n()
    const router = useRouter()
    const modules = ref([])
    const loading = ref(false)

    const loadModules = async () => {
      try {
        loading.value = true
        window.setLoading(true)
        
        const data = await contentAPI.getModules()
        modules.value = data.modules || []
      } catch (error) {
        console.error('Failed to load modules:', error)
        window.showNotification(t('modules.loadError'), 'error')
      } finally {
        loading.value = false
        window.setLoading(false)
      }
    }

    const getCompletedLessons = (module) => {
      return module.lessons.filter(lesson => 
        lesson.progress?.status === 'completed'
      ).length
    }

    const getModuleProgress = (module) => {
      const completed = getCompletedLessons(module)
      const total = module.lessons.length
      return total > 0 ? Math.round((completed / total) * 100) : 0
    }

    const getLessonStatusClass = (lesson) => {
      if (!lesson.is_unlocked) return 'lesson-locked'
      if (lesson.progress?.status === 'completed') return 'lesson-completed'
      if (lesson.progress?.status === 'in_progress') return 'lesson-in-progress'
      return 'lesson-not-started'
    }

    const getLessonIcon = (lesson) => {
      if (!lesson.is_unlocked) return ''
      if (lesson.progress?.status === 'completed') return ''
      if (lesson.progress?.status === 'in_progress') return ''
      return ''
    }

    const getProgressText = (progress) => {
      const statusMap = {
        'not_started': t('modules.progressStatus.notStarted'),
        'in_progress': t('modules.progressStatus.inProgress', { attempts: progress.attempts || 0 }),
        'completed': t('modules.progressStatus.completed')
      }
      return statusMap[progress.status] || t('modules.progressStatus.unknown')
    }

    const handleLessonClick = (lesson) => {
      if (!lesson.is_unlocked) {
        window.showNotification(t('modules.prerequisiteWarning'), 'warning')
        return
      }
      
      router.push(`/lesson/${lesson.lesson_id}`)
    }

    onMounted(() => {
      loadModules()
    })

    return {
      modules,
      loading,
      loadModules,
      getCompletedLessons,
      getModuleProgress,
      getLessonStatusClass,
      getLessonIcon,
      getProgressText,
      handleLessonClick,
      t
    }
  }
}
</script>

<style scoped>
.modules-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem 1rem 2rem 1rem;
  background-color: var(--color-slate-50);
  min-height: 100vh;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

/* 页面头部样式 */
.modules-header {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  padding: 3rem 2rem;
  text-align: center;
  margin-bottom: 2rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.modules-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-blue-600) 0%, var(--color-purple-600) 50%, var(--color-green-600) 100%);
  opacity: 0.8;
}

.modules-header:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.modules-header h1 {
  color: var(--color-slate-900);
  margin-bottom: 1rem;
  font-size: 2.5rem;
  font-weight: 800;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
  background: linear-gradient(135deg, var(--color-blue-600) 0%, var(--color-purple-600) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.modules-header p {
  color: var(--color-slate-600);
  font-size: 1.125rem;
  line-height: 1.6;
  margin: 0;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

/* 模块网格布局 */
.modules-grid {
  display: grid;
  gap: 2rem;
}

/* 模块卡片样式 - 现代化设计 */
.module-card {
  background: white;
  border-radius: 16px;
  padding: 2.5rem;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid var(--color-slate-100);
  position: relative;
  overflow: hidden;
}

.module-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-blue-600), var(--color-green-600));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.module-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 16px 40px rgba(0, 0, 0, 0.15);
  border-color: var(--color-blue-200);
}

.module-card:hover::before {
  opacity: 1;
}

/* 模块头部 */
.module-header {
  margin-bottom: 1.5rem;
}

.module-header h2 {
  color: var(--color-slate-900);
  margin-bottom: 1rem;
  font-size: 1.75rem;
  font-weight: 700;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

/* 进度条区域 */
.module-progress {
  margin-bottom: 1.5rem;
}

.progress-info {
  font-size: 0.875rem;
  color: var(--color-slate-600);
  margin-bottom: 0.875rem;
  font-weight: 600;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--color-slate-100);
  border-radius: 6px;
  overflow: hidden;
  box-shadow: inset 0 2px 6px rgba(0, 0, 0, 0.08);
  position: relative;
}

.progress-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 25%, rgba(255,255,255,0.1) 25%, rgba(255,255,255,0.1) 50%, transparent 50%, transparent 75%, rgba(255,255,255,0.1) 75%);
  background-size: 20px 20px;
  animation: progress-stripes 1s linear infinite;
}

@keyframes progress-stripes {
  0% { background-position: 0 0; }
  100% { background-position: 20px 0; }
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, var(--color-blue-600) 0%, var(--color-green-500) 100%);
  transition: width 1s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2.5s infinite;
  border-radius: 10px;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* 模块描述 */
.module-description {
  color: var(--color-slate-600);
  margin-bottom: 1.5rem;
  line-height: 1.7;
  font-size: 1rem;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

/* 课程列表 */
.lessons-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

/* 课程项目 - 现代化设计 */
.lesson-item {
  display: flex;
  align-items: center;
  padding: 1.25rem 1.5rem;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid transparent;
  position: relative;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.lesson-item:hover {
  transform: translateX(6px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* 课程状态样式 */
.lesson-not-started {
  background: linear-gradient(135deg, white 0%, var(--color-slate-50) 100%);
  border-color: var(--color-slate-200);
}

.lesson-not-started:hover {
  border-color: var(--color-blue-400);
  background: linear-gradient(135deg, var(--color-blue-50) 0%, white 100%);
  box-shadow: 0 6px 25px rgba(59, 130, 246, 0.15);
}

.lesson-not-started::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: var(--color-slate-300);
  border-radius: 0 4px 4px 0;
}

.lesson-in-progress {
  background: linear-gradient(135deg, var(--color-amber-50) 0%, white 100%);
  border-color: var(--color-amber-300);
}

.lesson-in-progress:hover {
  box-shadow: 0 6px 25px rgba(245, 158, 11, 0.2);
  border-color: var(--color-amber-400);
}

.lesson-in-progress::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: var(--color-amber-500);
  border-radius: 0 4px 4px 0;
}

.lesson-completed {
  background: linear-gradient(135deg, var(--color-green-50) 0%, white 100%);
  border-color: var(--color-green-300);
}

.lesson-completed:hover {
  box-shadow: 0 6px 25px rgba(16, 185, 129, 0.2);
  border-color: var(--color-green-400);
}

.lesson-completed::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: var(--color-green-500);
  border-radius: 0 4px 4px 0;
}

.lesson-locked {
  background: linear-gradient(135deg, var(--color-slate-100) 0%, white 100%);
  border-color: var(--color-slate-300);
  cursor: not-allowed;
  opacity: 0.6;
}

.lesson-locked:hover {
  transform: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.lesson-locked::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: var(--color-slate-400);
  border-radius: 0 4px 4px 0;
}

/* 课程状态图标 */
.lesson-status {
  font-size: 1.5rem;
  margin-right: 1rem;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-weight: 600;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.lesson-not-started .lesson-status {
  background: var(--color-blue-100);
  color: var(--color-blue-700);
}

.lesson-not-started .lesson-status::after {
  content: 'NEW';
}

.lesson-in-progress .lesson-status {
  background: var(--color-amber-100);
  color: var(--color-amber-700);
}

.lesson-in-progress .lesson-status::after {
  content: 'WIP';
}

.lesson-completed .lesson-status {
  background: var(--color-green-100);
  color: var(--color-green-700);
}

.lesson-completed .lesson-status::after {
  content: 'DONE';
}

.lesson-locked .lesson-status {
  background: var(--color-slate-200);
  color: var(--color-slate-600);
}

.lesson-locked .lesson-status::after {
  content: 'LOCK';
}

.lesson-item:hover .lesson-status {
  transform: scale(1.05);
}

/* 课程内容区域 */
.lesson-content {
  flex: 1;
}

.lesson-title {
  font-weight: 600;
  color: var(--color-slate-900);
  margin-bottom: 0.25rem;
  font-size: 1.1rem;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

.lesson-meta {
  font-size: 0.875rem;
  color: var(--color-slate-500);
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

.locked-text {
  color: var(--color-red-600);
  font-style: italic;
  font-weight: 500;
}

/* 箭头指示器 */
.lesson-arrow {
  font-size: 1.25rem;
  color: var(--color-blue-600);
  margin-left: 1rem;
  transition: all 0.3s ease;
  opacity: 0.6;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: var(--color-blue-50);
  border: 1px solid var(--color-blue-200);
}

.lesson-item:hover .lesson-arrow {
  opacity: 1;
  transform: translateX(4px) scale(1.1);
  background: var(--color-blue-100);
  border-color: var(--color-blue-300);
}

/* 加载状态 */
.loading-message {
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
  text-align: center;
  padding: 4rem 2rem;
  margin: 2rem 0;
  border: 1px solid var(--color-slate-100);
}

.loading-message .spinner {
  width: 3.5rem;
  height: 3.5rem;
  border: 4px solid var(--color-slate-100);
  border-top: 4px solid var(--color-blue-600);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 2rem;
  position: relative;
}

.loading-message .spinner::after {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border: 4px solid transparent;
  border-top: 4px solid var(--color-blue-300);
  border-radius: 50%;
  animation: spin 2s linear infinite reverse;
}

.loading-message p {
  color: var(--color-slate-600);
  font-size: 1.125rem;
  margin: 0;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
  font-weight: 500;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
  text-align: center;
  padding: 4rem 2rem;
  margin: 2rem 0;
  border: 1px solid var(--color-slate-100);
  position: relative;
}

.empty-state::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120px;
  height: 120px;
  background: linear-gradient(135deg, var(--color-blue-50) 0%, var(--color-purple-50) 100%);
  border-radius: 50%;
  opacity: 0.5;
  z-index: 0;
}

.empty-state h3 {
  margin-bottom: 1rem;
  color: var(--color-slate-900);
  font-size: 1.75rem;
  font-weight: 700;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
  position: relative;
  z-index: 1;
}

.empty-state p {
  margin-bottom: 2rem;
  color: var(--color-slate-600);
  font-size: 1.125rem;
  line-height: 1.6;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
  position: relative;
  z-index: 1;
}

/* 按钮样式 */
.btn {
  padding: 0.875rem 2rem;
  border-radius: 10px;
  font-weight: 600;
  transition: all 0.3s ease;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  border: 1px solid transparent;
  font-size: 1rem;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: white;
  color: var(--color-blue-600);
  border-color: var(--color-blue-200);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.btn-primary:hover {
  background: var(--color-blue-50);
  border-color: var(--color-blue-300);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.25);
  color: var(--color-blue-700);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modules-container {
    padding: 0.5rem;
  }
  
  .modules-header {
    padding: 1.5rem;
  }
  
  .modules-header h1 {
    font-size: 1.875rem;
  }
  
  .modules-header p {
    font-size: 1rem;
  }
  
  .module-card {
    padding: 1.5rem;
  }
  
  .module-header h2 {
    font-size: 1.5rem;
  }
  
  .lesson-item {
    padding: 0.875rem 1rem;
  }
  
  .lesson-item:hover {
    transform: none;
  }
  
  .lesson-arrow {
    display: none;
  }
}

@media (min-width: 1024px) {
  .modules-grid {
    grid-template-columns: repeat(auto-fit, minmax(600px, 1fr));
  }
}
</style>