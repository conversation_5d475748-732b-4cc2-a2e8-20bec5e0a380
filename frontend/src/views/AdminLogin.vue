<template>
  <div class="auth-container">
    <div class="auth-card">
      <h1 class="auth-title">🔐 {{ $t('admin.login.title') }}</h1>
      <p class="auth-subtitle">{{ $t('admin.login.platformSubtitle') }}</p>

      <form @submit.prevent="handleLogin" class="login-form">
        <div class="form-group">
          <label class="form-label">{{ $t('auth.username') }}</label>
          <input
            v-model="loginForm.username"
            type="text"
            class="form-input"
            :placeholder="$t('admin.login.usernamePlaceholder')"
            required
          />
        </div>

        <div class="form-group">
          <label class="form-label">{{ $t('auth.password') }}</label>
          <input
            v-model="loginForm.password"
            type="password"
            class="form-input"
            :placeholder="$t('auth.passwordPlaceholder')"
            required
          />
        </div>

        <button type="submit" class="btn btn-primary" :disabled="loading">
          {{ loading ? $t('auth.loggingIn') : $t('auth.login') }}
        </button>
      </form>

      <div class="auth-link">
        👨‍🎓 {{ $t('admin.login.backToStudent') }}
        <router-link to="/login">{{ $t('admin.login.studentLogin') }}</router-link>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { authAPI } from '../api.js'

export default {
  name: 'AdminLogin',
  setup() {
    const { t } = useI18n()
    const router = useRouter()
    const loading = ref(false)
    const loginForm = ref({
      username: '',
      password: ''
    })

    const handleLogin = async () => {
      try {
        loading.value = true
        
        const response = await authAPI.login(loginForm.value)
        
        // Check if user is admin
        if (response.user.role !== 'admin') {
          window.showNotification(t('admin.login.adminOnlyError'), 'error')
          return
        }
        
        // Login successful, redirect to admin dashboard
        window.showNotification(t('auth.loginSuccess'), 'success')
        router.push('/admin/dashboard')
        
      } catch (error) {
        console.error('Admin login failed:', error)
        const message = error.response?.data?.error || t('auth.loginError')
        window.showNotification(message, 'error')
      } finally {
        loading.value = false
      }
    }

    return {
      loginForm,
      loading,
      handleLogin,
      t
    }
  }
}
</script>

<style scoped>
/* AdminLogin 页面使用全局统一的认证样式 */
/* 所有样式都继承自 style.css 中的 .auth-* 类 */
</style>