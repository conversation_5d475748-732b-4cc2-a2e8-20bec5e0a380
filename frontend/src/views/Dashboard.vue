<template>
  <div class="dashboard-container">
    <div class="dashboard-header">
      <div class="header-content">
        <h1>{{ $t('dashboard.title') }}</h1>
        <p>{{ $t('dashboard.welcome', { username: userData?.username }) }}</p>
      </div>
      <div class="header-actions">
        <button @click="handleLogout" class="logout-btn">
          {{ $t('dashboard.logout') }}
        </button>
      </div>
    </div>

    <!-- Learning Progress Overview -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon total-lessons"></div>
        <div class="stat-content">
          <h3>{{ dashboardData?.overview?.total_lessons || 0 }}</h3>
          <p>{{ $t('dashboard.stats.totalLessons') }}</p>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon completed-lessons"></div>
        <div class="stat-content">
          <h3>{{ dashboardData?.overview?.completed_lessons || 0 }}</h3>
          <p>{{ $t('dashboard.stats.completedLessons') }}</p>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon in-progress-lessons"></div>
        <div class="stat-content">
          <h3>{{ dashboardData?.overview?.in_progress_lessons || 0 }}</h3>
          <p>{{ $t('dashboard.stats.inProgressLessons') }}</p>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon completion-rate"></div>
        <div class="stat-content">
          <h3>{{ dashboardData?.overview?.completion_rate || 0 }}%</h3>
          <p>{{ $t('dashboard.stats.completionRate') }}</p>
        </div>
      </div>
      
      <!-- 💡 NEW: Hint Usage Statistics -->
      <div class="stat-card hint-usage-card">
        <div class="stat-icon hints-used"></div>
        <div class="stat-content">
          <h3>{{ codeAnalysis?.overall_stats?.total_hints_used || 0 }}</h3>
          <p>{{ $t('dashboard.stats.hintsUsed') }}</p>
          <div v-if="codeAnalysis?.hint_analysis?.exercises_needing_hints" class="stat-subtitle">
            {{ $t('dashboard.stats.exercisesNeedingHints', { count: codeAnalysis.hint_analysis.exercises_needing_hints }) }}
          </div>
        </div>
      </div>
    </div>

    <!-- Progress Bar -->
    <div class="card">
      <h2 class="card-title">{{ $t('dashboard.progress.title') }}</h2>
      <div class="progress-container">
        <div class="progress-bar">
          <div 
            class="progress-fill" 
            :style="{ width: `${dashboardData?.overview?.completion_rate || 0}%` }"
          ></div>
        </div>
        <div class="progress-text">
          {{ $t('dashboard.progress.lessonsCompleted', { completed: dashboardData?.overview?.completed_lessons || 0, total: dashboardData?.overview?.total_lessons || 0 }) }}
        </div>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="card" v-if="dashboardData?.recent_activity?.length">
      <h2 class="card-title">{{ $t('dashboard.recentActivity.title') }}</h2>
      <div class="activity-list">
        <div 
          v-for="activity in dashboardData.recent_activity" 
          :key="activity.submission_id"
          class="activity-item"
        >
          <div class="activity-icon" :class="activity.is_correct ? 'success' : 'error'">
            <div class="activity-icon-inner" :class="activity.is_correct ? 'success' : 'error'"></div>
          </div>
          <div class="activity-content">
            <div class="activity-title">
              {{ activity.is_correct ? $t('dashboard.recentActivity.exerciseCompleted') : $t('dashboard.recentActivity.exerciseAttempt') }}
            </div>
            <div class="activity-time">
              {{ formatDate(activity.timestamp) }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 💡 NEW: Hint Usage Analysis -->
    <div class="card" v-if="codeAnalysis?.hint_analysis && codeAnalysis.hint_analysis.total_hints_used > 0">
      <h2 class="card-title">{{ $t('dashboard.hintAnalysis.title') }}</h2>
      <div class="hint-analysis-grid">
        <div class="analysis-card">
          <div class="analysis-icon hint-target"></div>
          <div class="analysis-content">
            <h3>{{ codeAnalysis.hint_analysis.total_hints_used }}</h3>
            <p>{{ $t('dashboard.hintAnalysis.totalHints') }}</p>
          </div>
        </div>
        
        <div class="analysis-card">
          <div class="analysis-icon hint-puzzle"></div>
          <div class="analysis-content">
            <h3>{{ codeAnalysis.hint_analysis.exercises_needing_hints }}</h3>
            <p>{{ $t('dashboard.hintAnalysis.exercisesWithHints') }}</p>
          </div>
        </div>
        
        <div class="analysis-card" v-if="getMostUsedHintLevel()">
          <div class="analysis-icon hint-chart"></div>
          <div class="analysis-content">
            <h3>{{ $t('dashboard.hintAnalysis.level') }} {{ getMostUsedHintLevel() }}</h3>
            <p>{{ $t('dashboard.hintAnalysis.mostUsedLevel') }}</p>
          </div>
        </div>
        
        <div class="analysis-card" v-if="getRecentHintTrend() !== null">
          <div class="analysis-icon hint-trend"></div>
          <div class="analysis-content">
            <h3>{{ getRecentHintTrend() }}</h3>
            <p>{{ $t('dashboard.hintAnalysis.recentTrend') }}</p>
            <div class="trend-indicator" :class="getRecentHintTrend() > 0 ? 'hint-increase' : 'hint-decrease'">
              {{ getRecentHintTrend() > 0 ? '↗' : '↘' }}
            </div>
          </div>
        </div>
      </div>
      
      <!-- Hint Level Distribution -->
      <div v-if="codeAnalysis.hint_analysis.hints_by_level && Object.keys(codeAnalysis.hint_analysis.hints_by_level).length > 0" class="hint-level-distribution">
        <h3>{{ $t('dashboard.hintAnalysis.levelDistribution') }}</h3>
        <div class="hint-level-chart">
          <div 
            v-for="(count, level) in codeAnalysis.hint_analysis.hints_by_level" 
            :key="level"
            class="hint-level-bar"
          >
            <span class="level-label">{{ $t('dashboard.hintAnalysis.level') }} {{ level }}</span>
            <div class="level-bar-container">
              <div class="level-bar-fill" :style="{ width: `${(count / maxHintCount) * 100}%` }"></div>
              <span class="level-count">{{ count }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Code Quality Analysis -->
    <div class="card" v-if="codeAnalysis">
      <h2 class="card-title">{{ $t('dashboard.codeQuality.title') }}</h2>
      <div class="code-analysis-grid">
        <div class="analysis-card">
          <div class="analysis-icon code-lines"></div>
          <div class="analysis-content">
            <h3>{{ codeAnalysis.overall_stats?.avg_code_lines || 0 }}</h3>
            <p>{{ $t('dashboard.codeQuality.averageLines') }}</p>
          </div>
        </div>
        
        <div class="analysis-card">
          <div class="analysis-icon code-complexity"></div>
          <div class="analysis-content">
            <h3>{{ codeAnalysis.overall_stats?.avg_complexity || 0 }}</h3>
            <p>{{ $t('dashboard.codeQuality.complexity') }}</p>
          </div>
        </div>
        
        <div class="analysis-card">
          <div class="analysis-icon code-syntax"></div>
          <div class="analysis-content">
            <h3>{{ codeAnalysis.overall_stats?.avg_syntax_score || 0 }}</h3>
            <p>{{ $t('dashboard.codeQuality.syntaxScore') }}</p>
          </div>
        </div>
        
        <div class="analysis-card">
          <div class="analysis-icon code-improvement"></div>
          <div class="analysis-content">
            <h3>{{ codeAnalysis.recent_trend?.improvement || 0 }}%</h3>
            <p>{{ $t('dashboard.codeQuality.recentImprovement') }}</p>
            <div class="trend-indicator" :class="codeAnalysis.recent_trend?.improvement >= 0 ? 'positive' : 'negative'">
              {{ codeAnalysis.recent_trend?.improvement >= 0 ? '↗' : '↘' }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Error Pattern Analysis -->
    <div class="card" v-if="dashboardData?.error_patterns?.length || codeAnalysis?.error_analysis">
      <h2 class="card-title">{{ $t('dashboard.errorPatterns.title') }}</h2>
      
      <!-- Original error patterns -->
      <div v-if="dashboardData?.error_patterns?.length" class="error-patterns">
        <div 
          v-for="pattern in dashboardData.error_patterns" 
          :key="pattern.error_type"
          :class="['error-pattern', pattern.error_type === 'no_error' ? 'success-pattern' : '']"
        >
          <div class="pattern-type">{{ getErrorTypeName(pattern.error_type) }}</div>
          <div class="pattern-count">{{ pattern.count }} {{ $t('dashboard.errorPatterns.times') }}</div>
        </div>
      </div>
      
      <!-- Enhanced code analysis error statistics -->
      <div v-if="codeAnalysis?.error_analysis" class="enhanced-error-analysis">
        <h3>{{ $t('dashboard.errorPatterns.detailedErrorAnalysis') }}</h3>
        <div class="error-chart">
          <div 
            v-for="(count, errorType) in codeAnalysis.error_analysis" 
            :key="errorType"
            :class="['error-bar', errorType === 'no_error' ? 'success-bar' : '']"
          >
            <span :class="['error-label', errorType === 'no_error' ? 'success-label' : '']">{{ getErrorTypeName(errorType) }}</span>
            <div class="error-bar-container">
              <div class="error-bar-fill" :style="{ width: `${(count / maxErrorCount) * 100}%` }"></div>
              <span class="error-count">{{ count }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
      <router-link to="/modules" class="btn btn-primary">
        {{ $t('dashboard.quickActions.continueLastLesson') }}
      </router-link>
      <button @click="loadDashboard" class="btn btn-secondary">
        {{ $t('dashboard.quickActions.refreshData') }}
      </button>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { progressAPI, authAPI, analyticsAPI } from '../api.js'

export default {
  name: 'Dashboard',
  setup() {
    const { t, locale } = useI18n()
    const dashboardData = ref(null)
    const codeAnalysis = ref(null)
    const loading = ref(false)

    const userData = computed(() => {
      const data = localStorage.getItem('user')
      return data ? JSON.parse(data) : null
    })

    const maxErrorCount = computed(() => {
      if (!codeAnalysis.value?.error_analysis) return 0
      return Math.max(...Object.values(codeAnalysis.value.error_analysis))
    })

    // 💡 NEW: Hint analysis computed properties
    const maxHintCount = computed(() => {
      if (!codeAnalysis.value?.hint_analysis?.hints_by_level) return 0
      return Math.max(...Object.values(codeAnalysis.value.hint_analysis.hints_by_level))
    })

    const loadDashboard = async () => {
      try {
        loading.value = true
        window.setLoading(true)
        
        // Load original dashboard data
        const data = await progressAPI.getDashboard()
        dashboardData.value = data
        
        // Load code analysis data
        try {
          const analysisData = await analyticsAPI.getUserCodeAnalysis()
          codeAnalysis.value = analysisData
        } catch (analysisError) {
          console.warn(t('dashboard.loadCodeAnalysisError'), analysisError)
          // Code analysis data loading failure does not affect main functionality
        }
        
      } catch (error) {
        console.error(t('dashboard.loadDashboardError'), error)
        if (window.showNotification) {
          window.showNotification(t('dashboard.loadDashboardError'), 'error')
        }
      } finally {
        loading.value = false
        window.setLoading(false)
      }
    }

    const formatDate = (timestamp) => {
      const localeString = locale.value === 'zh' ? 'zh-CN' : 'en-US'
      return new Date(timestamp).toLocaleString(localeString)
    }

    const getErrorTypeName = (errorType) => {
      const errorTypeKeys = {
        'syntax_error': 'syntaxError',
        'name_error': 'nameError',
        'type_error': 'typeError',
        'runtime_error': 'runtimeError',
        'logic_error': 'logicError',
        'timeout_error': 'timeoutError',
        'no_error': 'noError'
      }
      const key = errorTypeKeys[errorType]
      return key ? t(`dashboard.errorPatterns.${key}`) : errorType
    }

    // 💡 NEW: Hint analysis helper methods
    const getMostUsedHintLevel = () => {
      if (!codeAnalysis.value?.hint_analysis?.hints_by_level) return null
      const hintsByLevel = codeAnalysis.value.hint_analysis.hints_by_level
      const maxCount = Math.max(...Object.values(hintsByLevel))
      const mostUsedLevel = Object.keys(hintsByLevel).find(level => hintsByLevel[level] === maxCount)
      return mostUsedLevel
    }

    const getRecentHintTrend = () => {
      if (!codeAnalysis.value?.hint_analysis?.recent_hints_by_day) return null
      const recentHints = codeAnalysis.value.hint_analysis.recent_hints_by_day
      const days = Object.keys(recentHints).sort()
      if (days.length < 2) return null
      
      const recent = recentHints[days[days.length - 1]] || 0
      const previous = recentHints[days[days.length - 2]] || 0
      return recent - previous
    }

    const handleLogout = () => {
      if (confirm(t('dashboard.confirmLogout'))) {
        authAPI.logout()
      }
    }

    onMounted(() => {
      loadDashboard()
    })

    return {
      dashboardData,
      codeAnalysis,
      userData,
      loading,
      maxErrorCount,
      maxHintCount,
      loadDashboard,
      formatDate,
      getErrorTypeName,
      getMostUsedHintLevel,
      getRecentHintTrend,
      handleLogout,
      t,
      locale
    }
  }
}
</script>

<style scoped>
.dashboard-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem 1rem 2rem 1rem;
  background-color: var(--color-slate-50);
  min-height: 100vh;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

/* 仪表板头部样式 - 现代化设计 */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 2.5rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--color-slate-100);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.dashboard-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-blue-600) 0%, var(--color-purple-600) 50%, var(--color-green-600) 100%);
  opacity: 0.8;
}

.dashboard-header:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
}

.header-content {
  flex: 1;
}

.dashboard-header h1 {
  color: var(--color-slate-900);
  margin-bottom: 0.75rem;
  font-size: 2.5rem;
  font-weight: 800;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
  background: linear-gradient(135deg, var(--color-blue-600) 0%, var(--color-purple-600) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.dashboard-header p {
  color: var(--color-slate-600);
  font-size: 1.125rem;
  margin: 0;
  line-height: 1.6;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
}

/* 统计卡片网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

/* 统计卡片样式 - 现代化设计 */
.stat-card {
  background: white;
  border-radius: 16px;
  padding: 2.5rem;
  display: flex;
  align-items: center;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid var(--color-slate-100);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-blue-600), var(--color-green-600));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 16px 40px rgba(0, 0, 0, 0.15);
}

.stat-card:hover::before {
  opacity: 1;
}

/* 统计图标样式 - 现代化图标系统 */
.stat-icon {
  width: 4rem;
  height: 4rem;
  margin-right: 1.5rem;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  position: relative;
}

.stat-icon::before {
  content: '';
  width: 2rem;
  height: 2rem;
  border-radius: 4px;
}

/* 各种统计图标的样式 */
.stat-icon.total-lessons {
  background: linear-gradient(135deg, var(--color-blue-100), var(--color-blue-200));
}

.stat-icon.total-lessons::before {
  background: var(--color-blue-600);
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253'/%3E%3C/svg%3E") no-repeat center;
  mask-size: contain;
}

.stat-icon.completed-lessons {
  background: linear-gradient(135deg, var(--color-green-100), var(--color-green-200));
}

.stat-icon.completed-lessons::before {
  background: var(--color-green-600);
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z'/%3E%3C/svg%3E") no-repeat center;
  mask-size: contain;
}

.stat-icon.in-progress-lessons {
  background: linear-gradient(135deg, var(--color-amber-100), var(--color-amber-200));
}

.stat-icon.in-progress-lessons::before {
  background: var(--color-amber-600);
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z'/%3E%3C/svg%3E") no-repeat center;
  mask-size: contain;
}

.stat-icon.completion-rate {
  background: linear-gradient(135deg, var(--color-purple-100), var(--color-purple-200));
}

.stat-icon.completion-rate::before {
  background: var(--color-purple-600);
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z'/%3E%3C/svg%3E") no-repeat center;
  mask-size: contain;
}

.stat-icon.hints-used {
  background: linear-gradient(135deg, var(--color-amber-100), var(--color-amber-200));
}

.stat-icon.hints-used::before {
  background: var(--color-amber-600);
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z'/%3E%3C/svg%3E") no-repeat center;
  mask-size: contain;
}

.stat-card:hover .stat-icon {
  transform: scale(1.1) rotate(5deg);
}

/* 统计内容样式 */
.stat-content h3 {
  font-size: 2.25rem;
  margin: 0;
  color: var(--color-slate-900);
  font-weight: 700;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

.stat-content p {
  margin: 0.5rem 0 0;
  color: var(--color-slate-500);
  font-size: 0.875rem;
  font-weight: 500;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

/* 通用卡片样式 */
.card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--color-slate-200);
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.card-title {
  color: var(--color-slate-900);
  margin: 0 0 1.5rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* 进度条样式 - 增强版 */
.progress-container {
  margin-top: 1.5rem;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--color-slate-100);
  border-radius: 6px;
  overflow: hidden;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.06);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-blue-600) 0%, var(--color-green-600) 100%);
  transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.progress-text {
  text-align: center;
  margin-top: 0.75rem;
  color: var(--color-slate-500);
  font-weight: 500;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

/* 活动列表样式 */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 1.25rem;
  background: linear-gradient(135deg, var(--color-slate-50) 0%, whitefff 100%);
  border-radius: 10px;
  border: 1px solid var(--color-slate-200);
  transition: all 0.3s ease;
}

.activity-item:hover {
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

/* 活动图标样式 */
.activity-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  transition: transform 0.2s ease;
}

.activity-icon-inner {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 2px;
}

.activity-item:hover .activity-icon {
  transform: scale(1.1);
}

.activity-icon.success {
  background: linear-gradient(135deg, var(--color-green-50), var(--color-green-100));
  border: 2px solid var(--color-green-300);
}

.activity-icon-inner.success {
  background: var(--color-green-600);
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M5 13l4 4L19 7'/%3E%3C/svg%3E") no-repeat center;
  mask-size: contain;
}

.activity-icon.error {
  background: linear-gradient(135deg, var(--color-red-50), var(--color-red-100));
  border: 2px solid var(--color-red-300);
}

.activity-icon-inner.error {
  background: var(--color-red-600);
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M6 18L18 6M6 6l12 12'/%3E%3C/svg%3E") no-repeat center;
  mask-size: contain;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-weight: 600;
  color: var(--color-slate-900);
  margin-bottom: 0.25rem;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

.activity-time {
  font-size: 0.875rem;
  color: var(--color-slate-500);
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

/* 错误模式样式 */
.error-patterns {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 1rem;
}

.error-pattern {
  padding: 1.5rem;
  background: linear-gradient(135deg, var(--color-slate-50) 0%, whitefff 100%);
  border-radius: 10px;
  text-align: center;
  border: 1px solid var(--color-slate-200);
  transition: all 0.3s ease;
}

.error-pattern:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(231, 76, 60, 0.1);
}

.pattern-type {
  font-weight: 600;
  color: var(--color-slate-900);
  margin-bottom: 0.75rem;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

.pattern-count {
  font-size: 2rem;
  font-weight: 700;
  color: var(--color-red-600);
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

/* 成功模式（无错误）的绿色样式 */
.success-pattern {
  background: linear-gradient(135deg, var(--color-green-50) 0%, var(--color-green-100) 100%) !important;
  border: 1px solid var(--color-green-500) !important;
}

.success-pattern:hover {
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.2) !important;
}

.success-pattern .pattern-count {
  color: var(--color-green-800) !important;
}

.success-pattern .pattern-type {
  color: var(--color-green-700) !important;
}

/* 快速操作区域 */
.quick-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
  flex-wrap: wrap;
}

/* 按钮样式 */
.btn {
  padding: 0.875rem 2rem;
  border-radius: 10px;
  font-weight: 600;
  transition: all 0.3s ease;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  border: 1px solid transparent;
  font-size: 1rem;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: white;
  color: var(--color-blue-600);
  border-color: var(--color-blue-200);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.btn-primary:hover {
  background: var(--color-blue-50);
  border-color: var(--color-blue-300);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.25);
  color: var(--color-blue-700);
}

.btn-secondary {
  background: white;
  color: var(--color-slate-600);
  border-color: var(--color-slate-200);
  box-shadow: 0 4px 12px rgba(148, 163, 184, 0.15);
}

.btn-secondary:hover {
  background: var(--color-slate-50);
  border-color: var(--color-slate-300);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(148, 163, 184, 0.25);
  color: var(--color-slate-700);
}

/* 代码质量分析样式 */
.code-analysis-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 1rem;
  margin-top: 1.5rem;
}

.analysis-card {
  background: linear-gradient(135deg, var(--color-slate-50) 0%, whitefff 100%);
  border-radius: 10px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  border: 1px solid var(--color-slate-200);
}

.analysis-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

/* 分析图标样式 - 现代化图标系统 */
.analysis-icon {
  width: 3rem;
  height: 3rem;
  margin-right: 1rem;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  position: relative;
}

.analysis-icon::before {
  content: '';
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 2px;
}

/* 提示分析图标 */
.analysis-icon.hint-target {
  background: linear-gradient(135deg, var(--color-amber-100), var(--color-amber-200));
}

.analysis-icon.hint-target::before {
  background: var(--color-amber-600);
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4'/%3E%3C/svg%3E") no-repeat center;
  mask-size: contain;
}

.analysis-icon.hint-puzzle {
  background: linear-gradient(135deg, var(--color-blue-100), var(--color-blue-200));
}

.analysis-icon.hint-puzzle::before {
  background: var(--color-blue-600);
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z'/%3E%3C/svg%3E") no-repeat center;
  mask-size: contain;
}

.analysis-icon.hint-chart {
  background: linear-gradient(135deg, var(--color-green-100), var(--color-green-200));
}

.analysis-icon.hint-chart::before {
  background: var(--color-green-600);
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z'/%3E%3C/svg%3E") no-repeat center;
  mask-size: contain;
}

.analysis-icon.hint-trend {
  background: linear-gradient(135deg, var(--color-purple-100), var(--color-purple-200));
}

.analysis-icon.hint-trend::before {
  background: var(--color-purple-600);
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M13 7h8m0 0v8m0-8l-8 8-4-4-6 6'/%3E%3C/svg%3E") no-repeat center;
  mask-size: contain;
}

/* 代码质量图标 */
.analysis-icon.code-lines {
  background: linear-gradient(135deg, var(--color-slate-100), var(--color-slate-200));
}

.analysis-icon.code-lines::before {
  background: var(--color-slate-600);
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4'/%3E%3C/svg%3E") no-repeat center;
  mask-size: contain;
}

.analysis-icon.code-complexity {
  background: linear-gradient(135deg, var(--color-red-100), var(--color-red-200));
}

.analysis-icon.code-complexity::before {
  background: var(--color-red-600);
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z'/%3E%3C/svg%3E") no-repeat center;
  mask-size: contain;
}

.analysis-icon.code-syntax {
  background: linear-gradient(135deg, var(--color-indigo-100), var(--color-indigo-200));
}

.analysis-icon.code-syntax::before {
  background: var(--color-indigo-600);
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z'/%3E%3C/svg%3E") no-repeat center;
  mask-size: contain;
}

.analysis-icon.code-improvement {
  background: linear-gradient(135deg, var(--color-green-100), var(--color-green-200));
}

.analysis-icon.code-improvement::before {
  background: var(--color-green-600);
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M13 7h8m0 0v8m0-8l-8 8-4-4-6 6'/%3E%3C/svg%3E") no-repeat center;
  mask-size: contain;
}

.analysis-card:hover .analysis-icon {
  transform: scale(1.1) rotate(5deg);
}

.analysis-content {
  flex: 1;
}

.analysis-content h3 {
  font-size: 1.75rem;
  margin: 0;
  color: var(--color-slate-900);
  font-weight: 700;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

.analysis-content p {
  margin: 0.5rem 0 0;
  color: var(--color-slate-500);
  font-size: 0.875rem;
  font-weight: 500;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

/* 趋势指示器 */
.trend-indicator {
  display: inline-block;
  font-size: 1.25rem;
  margin-left: 0.5rem;
  transition: transform 0.2s ease;
}

.analysis-card:hover .trend-indicator {
  transform: scale(1.2);
}

.trend-indicator.positive {
  color: var(--color-green-500);
}

.trend-indicator.negative {
  color: var(--color-red-500);
}

/* 增强的错误分析样式 */
.enhanced-error-analysis {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 2px solid var(--color-slate-100);
}

.enhanced-error-analysis h3 {
  color: var(--color-slate-900);
  margin-bottom: 1rem;
  font-weight: 700;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

.error-chart {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.error-bar {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.error-label {
  min-width: 120px;
  font-size: 0.875rem;
  color: var(--color-slate-900);
  font-weight: 500;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

.error-bar-container {
  flex: 1;
  height: 1.5rem;
  background: var(--color-slate-100);
  border-radius: 12px;
  position: relative;
  overflow: hidden;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.error-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-red-500), var(--color-red-600));
  border-radius: 12px;
  transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 成功条（无错误）的绿色样式 */
.success-bar .error-bar-fill {
  background: linear-gradient(90deg, var(--color-green-500), var(--color-green-600)) !important;
}

.success-label {
  color: var(--color-green-800) !important;
  font-weight: 600 !important;
}

.error-count {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.75rem;
  color: var(--color-slate-900);
  font-weight: 600;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

/* 💡 提示使用统计样式 */
.hint-usage-card {
  background: linear-gradient(135deg, var(--color-amber-50) 0%, var(--color-amber-100) 100%);
  border: 2px solid var(--color-amber-500);
}

.hint-usage-card .stat-icon {
  color: var(--color-amber-600);
}

.stat-subtitle {
  font-size: 0.75rem;
  color: var(--color-amber-800);
  margin-top: 0.25rem;
  font-weight: 500;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

.hint-analysis-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 1rem;
  margin-top: 1.5rem;
}

.hint-level-distribution {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 2px solid var(--color-slate-100);
}

.hint-level-distribution h3 {
  color: var(--color-slate-900);
  margin-bottom: 1rem;
  font-weight: 700;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

.hint-level-chart {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.hint-level-bar {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.level-label {
  min-width: 80px;
  font-size: 0.875rem;
  color: var(--color-slate-900);
  font-weight: 600;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

.level-bar-container {
  flex: 1;
  height: 1.5rem;
  background: var(--color-slate-50);
  border-radius: 12px;
  position: relative;
  overflow: hidden;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.level-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-amber-500), var(--color-amber-600));
  border-radius: 12px;
  transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.level-count {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.75rem;
  color: var(--color-slate-900);
  font-weight: 600;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
}

.trend-indicator.hint-increase {
  color: var(--color-amber-500);
}

.trend-indicator.hint-decrease {
  color: var(--color-green-500);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 0.5rem;
  }
  
  .dashboard-header {
    padding: 1.5rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .dashboard-header h1 {
    font-size: 1.875rem;
  }
  
  .dashboard-header p {
    font-size: 1rem;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .stat-card {
    padding: 1.5rem;
  }
  
  .stat-icon {
    font-size: 2.5rem;
    margin-right: 1rem;
  }
  
  .card {
    padding: 1.5rem;
  }
  
  .quick-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .btn {
    width: 100%;
    justify-content: center;
    max-width: 300px;
  }
}
</style>