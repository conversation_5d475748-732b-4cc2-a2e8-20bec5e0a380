<template>
  <div class="research-analytics-container">
    <!-- 页面头部 -->
    <div class="research-header">
      <div class="header-content">
        <h1>{{ $t('research.title') }}</h1>
        <p class="research-subtitle">{{ $t('research.subtitle') }}</p>
        <div class="academic-badge">
          <span>{{ $t('research.academicBadge') }}</span>
        </div>
      </div>
    </div>

    <!-- 研究概览统计 -->
    <div class="research-overview" v-if="researchData">
      <h2 class="section-title">📊 {{ $t('research.overview.title') }}</h2>
      <div class="overview-grid">
        <div class="overview-card">
          <div class="card-icon">🎯</div>
          <div class="card-content">
            <h3>{{ researchData.overview?.total_feedback_instances || 0 }}</h3>
            <p>{{ $t('research.overview.totalFeedback') }}</p>
          </div>
        </div>
        
        <div class="overview-card">
          <div class="card-icon">📈</div>
          <div class="card-content">
            <h3>{{ researchData.overview?.total_effectiveness_records || 0 }}</h3>
            <p>{{ $t('research.overview.totalRecords') }}</p>
          </div>
        </div>
        
        <div class="overview-card">
          <div class="card-icon">✅</div>
          <div class="card-content">
            <h3>{{ researchData.overview?.total_successful_cases || 0 }}</h3>
            <p>{{ $t('research.overview.successfulCases') }}</p>
          </div>
        </div>
        
        <div class="overview-card success">
          <div class="card-icon">🏆</div>
          <div class="card-content">
            <h3>{{ researchData.overview?.overall_success_rate || 0 }}%</h3>
            <p>{{ $t('research.overview.successRate') }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 核心研究问题 -->
    <div class="research-question" v-if="researchData?.academic_insights">
      <h2 class="section-title">🔍 {{ $t('research.question.title') }}</h2>
      <div class="question-content">
        <div class="question-item">
          <strong>{{ $t('research.question.main') }}</strong>
          <p>{{ researchData.academic_insights.research_question }}</p>
        </div>
        <div class="question-item">
          <strong>{{ $t('research.question.methodology') }}</strong>
          <p>{{ researchData.academic_insights.methodology }}</p>
        </div>
        <div class="question-item">
          <strong>{{ $t('research.question.period') }}</strong>
          <p>{{ researchData.academic_insights.data_collection_period }}</p>
        </div>
      </div>
    </div>

    <!-- 反馈效果对比矩阵 - 论文核心图表 -->
    <div class="effectiveness-matrix">
      <h2 class="section-title">📋 {{ $t('research.matrix.title') }}</h2>
      <p class="matrix-description">{{ $t('research.matrix.description') }}</p>
      
      <!-- 矩阵加载状态 -->
      <div v-if="loading" class="matrix-loading">
        <div class="loading-spinner">🔄</div>
        <p>{{ $t('research.loading') }}</p>
      </div>
      
      <!-- 矩阵错误状态 -->
      <div v-else-if="error" class="matrix-error">
        <div class="error-icon">❌</div>
        <p>{{ error }}</p>
        <button @click="loadResearchData" class="btn btn-secondary">{{ $t('research.retry') }}</button>
      </div>
      
      <!-- 矩阵数据为空状态 -->
      <div v-else-if="!researchData?.research_matrix || !researchData?.feedback_types?.length" class="matrix-empty">
        <div class="empty-icon">📊</div>
        <p>{{ $t('research.matrix.preparing') }}</p>
        <button @click="loadResearchData" class="btn btn-primary">🔄 {{ $t('research.matrix.reload') }}</button>
      </div>
              
      <!-- 矩阵数据正常显示 -->
      <div v-else class="matrix-container">
        <div class="matrix-header">
          <div class="corner-cell"></div>
          <div 
            v-for="feedbackType in researchData.feedback_types" 
            :key="feedbackType"
            class="header-cell"
          >
            {{ getFeedbackTypeLabel(feedbackType) }}
          </div>
        </div>
        
        <div 
          v-for="errorType in researchData.error_types" 
          :key="errorType"
          class="matrix-row"
        >
          <div class="row-header">
            {{ getErrorTypeLabel(errorType) }}
          </div>
          <div 
            v-for="feedbackType in researchData.feedback_types" 
            :key="feedbackType"
            class="matrix-cell"
            :class="getMatrixCellClass(feedbackType, errorType)"
          >
            <div class="cell-content" v-if="getMatrixData(feedbackType, errorType)">
              <div class="metric-primary">
                {{ getMatrixData(feedbackType, errorType).avg_success_time?.toFixed(1) || 'N/A' }}s
              </div>
              <div class="metric-secondary">
                {{ getMatrixData(feedbackType, errorType).avg_attempts?.toFixed(1) || 'N/A' }} {{ $t('research.matrix.attempts') }}
              </div>
              <div class="metric-rating">
                ⭐ {{ getMatrixData(feedbackType, errorType).avg_helpfulness?.toFixed(1) || 'N/A' }}
              </div>
              <div class="sample-size">
                (n={{ getMatrixData(feedbackType, errorType).sample_size || 0 }})
              </div>
            </div>
            <div v-else class="no-data">
              {{ $t('research.matrix.noData') }}
            </div>
          </div>
        </div>
      </div>
      
      <div class="matrix-legend">
        <h4>{{ $t('research.matrix.legend') }}</h4>
        <div class="legend-items">
          <div class="legend-item">
            <span class="legend-color excellent"></span>
            <span>{{ $t('research.matrix.excellent') }} (&lt; 30s)</span>
          </div>
          <div class="legend-item">
            <span class="legend-color good"></span>
            <span>{{ $t('research.matrix.good') }} (30-60s)</span>
          </div>
          <div class="legend-item">
            <span class="legend-color moderate"></span>
            <span>{{ $t('research.matrix.moderate') }} (60-120s)</span>
          </div>
          <div class="legend-item">
            <span class="legend-color poor"></span>
            <span>{{ $t('research.matrix.poor') }} (&gt; 120s)</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 研究发现和论文要点 -->
    <div class="research-insights">
      <h2 class="section-title">💡 {{ $t('research.insights.title') }}</h2>
      <div class="insights-grid">
        <div class="insight-card">
          <h3>{{ $t('research.insights.dataIntegrity') }}</h3>
          <p>{{ $t('research.insights.dataIntegrityDesc') }}</p>
        </div>
        <div class="insight-card">
          <h3>{{ $t('research.insights.feedbackComparison') }}</h3>
          <p>{{ $t('research.insights.feedbackComparisonDesc') }}</p>
        </div>
        <div class="insight-card">
          <h3>{{ $t('research.insights.userExperience') }}</h3>
          <p>{{ $t('research.insights.userExperienceDesc') }}</p>
        </div>
      </div>
    </div>

    <!-- 数据导出和引用 -->
    <div class="data-export">
      <h2 class="section-title">📤 {{ $t('research.export.title') }}</h2>
      <div class="export-buttons">
        <button @click="exportData('csv')" class="btn btn-primary">
          📊 {{ $t('research.export.csv') }}
        </button>
        <button @click="exportData('json')" class="btn btn-secondary">
          📄 {{ $t('research.export.json') }}
        </button>
        <button @click="generateCitation" class="btn btn-info">
          📑 {{ $t('research.export.citation') }}
        </button>
      </div>
      <div v-if="citation" class="citation-text">
        <h4>{{ $t('research.export.citationFormat') }}</h4>
        <code>{{ citation }}</code>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="spinner"></div>
      <p>{{ $t('research.loading') }}</p>
    </div>

    <!-- 错误状态 -->
    <div v-if="error" class="error-container">
      <h3>{{ $t('research.error') }}</h3>
      <p>{{ error }}</p>
      <button @click="loadResearchData" class="btn btn-primary">
        🔄 {{ $t('research.retry') }}
      </button>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { analyticsAPI } from '../api.js'

export default {
  name: 'ResearchAnalytics',
  setup() {
    const { t } = useI18n()
    const researchData = ref(null)
    const loading = ref(false)
    const error = ref(null)
    const citation = ref('')

    const loadResearchData = async () => {
      try {
        loading.value = true
        error.value = null
        
        const data = await analyticsAPI.getFeedbackEffectivenessAnalysis()
        researchData.value = data
        
      } catch (err) {
        console.error('Failed to load research data:', err)
        error.value = err.response?.data?.error || t('research.loadError')
      } finally {
        loading.value = false
      }
    }

    const getFeedbackTypeLabel = (feedbackType) => {
      const labels = {
        'judge0_structural': t('research.feedbackTypes.structural'),
        'openai_educational': t('research.feedbackTypes.openai'),
        'deepseek_educational': t('research.feedbackTypes.deepseek'),
        'local_rules': t('research.feedbackTypes.localRules')
      }
      return labels[feedbackType] || feedbackType
    }

    const getErrorTypeLabel = (errorType) => {
      const labels = {
        'syntax_error': t('research.errorTypes.syntax'),
        'name_error': t('research.errorTypes.name'),
        'type_error': t('research.errorTypes.type'),
        'logic_error': t('research.errorTypes.logic'),
        'runtime_error': t('research.errorTypes.runtime'),
        'no_error': t('research.errorTypes.noError')
      }
      return labels[errorType] || errorType
    }

    const getMatrixData = (feedbackType, errorType) => {
      return researchData.value?.research_matrix?.[feedbackType]?.[errorType]
    }

    const getMatrixCellClass = (feedbackType, errorType) => {
      const data = getMatrixData(feedbackType, errorType)
      if (!data || !data.avg_success_time) return 'no-data-cell'
      
      const time = data.avg_success_time
      if (time < 30) return 'cell-excellent'
      if (time < 60) return 'cell-good'
      if (time < 120) return 'cell-moderate'
      return 'cell-poor'
    }

    const exportData = (format) => {
      if (!researchData.value) return
      
      const dataStr = format === 'json' 
        ? JSON.stringify(researchData.value, null, 2)
        : convertToCSV(researchData.value)
      
      const blob = new Blob([dataStr], { 
        type: format === 'json' ? 'application/json' : 'text/csv' 
      })
      
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `research_data.${format}`
      link.click()
      window.URL.revokeObjectURL(url)
    }

    const convertToCSV = (data) => {
      // Complete CSV conversion - includes all research metrics
      let csv = 'FeedbackType,ErrorType,AvgSuccessTime,AvgAttempts,AvgHelpfulness,SampleSize,SuccessRate\n'
      
      for (const feedbackType in data.research_matrix) {
        for (const errorType in data.research_matrix[feedbackType]) {
          const item = data.research_matrix[feedbackType][errorType]
          csv += `${feedbackType},${errorType},${item.avg_success_time},${item.avg_attempts},${item.avg_helpfulness},${item.sample_size},${item.success_rate}\n`
        }
      }
      
      return csv
    }

    const generateCitation = () => {
      const currentYear = new Date().getFullYear()
      citation.value = `[Author Name]. (${currentYear}). Comparative Analysis of AI-Driven Feedback in Programming Education: Evidence from Interactive Tutoring System. MSc Computer Science Dissertation, University of [Name].`
    }

    onMounted(() => {
      loadResearchData()
    })

    return {
      researchData,
      loading,
      error,
      citation,
      loadResearchData,
      getFeedbackTypeLabel,
      getErrorTypeLabel,
      getMatrixData,
      getMatrixCellClass,
      exportData,
      generateCitation,
      t
    }
  }
}
</script>

<style scoped>
.research-analytics-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  background: var(--color-slate-50);
  min-height: 100vh;
}

.research-header {
  background: linear-gradient(135deg, var(--color-blue-500) 0%, var(--color-blue-600) 100%);
  color: white;
  border-radius: 16px;
  padding: 40px;
  margin-bottom: 30px;
  text-align: center;
}

.research-header h1 {
  margin: 0 0 16px 0;
  font-size: 2.5rem;
}

.research-subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  margin: 0 0 24px 0;
}

.academic-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.2);
  padding: 8px 16px;
  border-radius: 24px;
  font-size: 14px;
}

.section-title {
  color: var(--color-slate-800);
  margin-bottom: 20px;
  font-size: 1.5rem;
}

.research-overview,
.research-question,
.effectiveness-matrix,
.research-insights,
.data-export {
  background: white;
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.overview-card {
  background: var(--color-slate-50);
  border-radius: 8px;
  padding: 24px;
  display: flex;
  align-items: center;
  transition: transform 0.3s ease;
}

.overview-card:hover {
  transform: translateY(-2px);
}

.overview-card.success {
  background: linear-gradient(135deg, var(--color-green-50), #c3e6cb);
  border: 1px solid var(--color-green-600);
}

.card-icon {
  font-size: 2.5rem;
  margin-right: 20px;
}

.card-content h3 {
  font-size: 2rem;
  margin: 0;
  color: var(--color-slate-800);
}

.card-content p {
  margin: 5px 0 0;
  color: var(--color-slate-500);
  font-size: 14px;
}

.question-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.question-item {
  padding: 20px;
  background: var(--color-slate-50);
  border-radius: 8px;
  border-left: 4px solid var(--color-blue-600);
}

.question-item strong {
  color: var(--color-slate-800);
}

.question-item p {
  margin: 8px 0 0;
  color: var(--color-slate-500);
}

.matrix-description {
  color: var(--color-slate-500);
  font-style: italic;
  margin-bottom: 24px;
}

.matrix-container {
  border: 1px solid var(--color-slate-300);
  border-radius: 8px;
  overflow: hidden;
}

.matrix-header {
  display: flex;
  background: var(--color-slate-200);
  font-weight: bold;
}

.corner-cell {
  min-width: 120px;
  padding: 16px;
  border-right: 1px solid var(--color-slate-300);
}

.header-cell {
  flex: 1;
  padding: 16px;
  border-right: 1px solid var(--color-slate-300);
  text-align: center;
  color: var(--color-slate-800);
}

.matrix-row {
  display: flex;
  border-bottom: 1px solid var(--color-slate-300);
}

.row-header {
  min-width: 120px;
  padding: 16px;
  background: var(--color-slate-50);
  border-right: 1px solid var(--color-slate-300);
  font-weight: 500;
  color: var(--color-slate-800);
}

.matrix-cell {
  flex: 1;
  padding: 12px;
  border-right: 1px solid var(--color-slate-300);
  text-align: center;
  min-height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cell-excellent {
  background: linear-gradient(135deg, var(--color-green-50), #c3e6cb);
}

.cell-good {
  background: linear-gradient(135deg, #d1ecf1, #bee5eb);
}

.cell-moderate {
  background: linear-gradient(135deg, var(--color-amber-50), #ffeaa7);
}

.cell-poor {
  background: linear-gradient(135deg, var(--color-red-50), #f5c6cb);
}

.no-data-cell {
  background: var(--color-slate-50);
  color: var(--color-slate-500);
}

.cell-content {
  text-align: center;
}

.metric-primary {
  font-size: 1.2rem;
  font-weight: bold;
  color: var(--color-slate-800);
  margin-bottom: 4px;
}

.metric-secondary {
  font-size: 0.9rem;
  color: var(--color-slate-500);
  margin-bottom: 4px;
}

.metric-rating {
  font-size: 0.85rem;
  color: var(--color-amber-600);
  margin-bottom: 4px;
}

.sample-size {
  font-size: 0.75rem;
  color: var(--color-slate-500);
}

.no-data {
  color: var(--color-slate-500);
  font-style: italic;
}

.matrix-legend {
  padding: 20px;
  background: var(--color-slate-50);
  border-top: 1px solid var(--color-slate-300);
}

.matrix-legend h4 {
  margin: 0 0 16px 0;
  color: var(--color-slate-800);
}

.legend-items {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.legend-color {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  border: 1px solid var(--color-slate-300);
}

.legend-color.excellent {
  background: linear-gradient(135deg, var(--color-green-50), #c3e6cb);
}

.legend-color.good {
  background: linear-gradient(135deg, #d1ecf1, #bee5eb);
}

.legend-color.moderate {
  background: linear-gradient(135deg, var(--color-amber-50), #ffeaa7);
}

.legend-color.poor {
  background: linear-gradient(135deg, var(--color-red-50), #f5c6cb);
}

/* 矩阵状态样式 */
.matrix-loading,
.matrix-error,
.matrix-empty {
  text-align: center;
  padding: 40px 20px;
  background: white;
  border-radius: 12px;
  border: 2px dashed var(--color-slate-200);
  margin: 20px 0;
}

.matrix-loading {
  border-color: var(--color-blue-600);
}

.matrix-error {
  border-color: var(--color-red-600);
}

.matrix-empty {
  border-color: var(--color-amber-600);
}

.loading-spinner,
.error-icon,
.empty-icon {
  font-size: 3rem;
  margin-bottom: 15px;
  display: block;
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.insight-card {
  padding: 24px;
  background: var(--color-slate-50);
  border-radius: 8px;
  border-left: 4px solid var(--color-green-600);
}

.insight-card h3 {
  color: var(--color-slate-800);
  margin: 0 0 12px 0;
}

.insight-card p {
  color: var(--color-slate-500);
  line-height: 1.6;
  margin: 0;
}

.export-buttons {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.citation-text {
  background: var(--color-slate-50);
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid var(--color-blue-600);
}

.citation-text h4 {
  margin: 0 0 12px 0;
  color: var(--color-slate-800);
}

.citation-text code {
  background: white;
  padding: 12px;
  border-radius: 4px;
  display: block;
  font-family: monospace;
  line-height: 1.4;
  border: 1px solid var(--color-slate-300);
}

.loading-container,
.error-container {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  margin-top: 30px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--color-slate-200);
  border-top: 4px solid var(--color-blue-600);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .matrix-header,
  .matrix-row {
    font-size: 12px;
  }
  
  .corner-cell,
  .row-header {
    min-width: 80px;
  }
  
  .matrix-cell {
    min-height: 80px;
    padding: 8px;
  }
  
  .legend-items {
    flex-direction: column;
    gap: 12px;
  }
}
</style>