<template>
  <div class="admin-users">
    <div class="page-header">
      <h1>{{ $t('admin.users.title') }}</h1>
      <div class="header-stats">
        <div class="stat-item">
          <span class="stat-value">{{ pagination?.total || 0 }}</span>
          <span class="stat-label">{{ $t('admin.users.totalUsers') }}</span>
        </div>
      </div>
    </div>

    <!-- 用户列表 -->
    <div class="users-table-container">
      <table class="users-table">
        <thead>
          <tr>
            <th>{{ $t('admin.users.table.userId') }}</th>
            <th>{{ $t('admin.users.table.username') }}</th>
            <th>{{ $t('admin.users.table.email') }}</th>
            <th>{{ $t('admin.users.table.registeredAt') }}</th>
            <th>{{ $t('admin.users.table.learningProgress') }}</th>
            <th>{{ $t('admin.users.table.completionRate') }}</th>
            <th>{{ $t('admin.users.table.actions') }}</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="user in users" :key="user.user_id" class="user-row">
            <td>{{ user.user_id }}</td>
            <td>
              <div class="user-info">
                <span class="username">{{ user.username }}</span>
              </div>
            </td>
            <td>{{ user.email }}</td>
            <td>{{ formatDate(user.created_at) }}</td>
            <td>
              <div class="progress-info">
                <span>{{ user.learning_stats?.completed_lessons || 0 }} / {{ user.learning_stats?.total_lessons || 0 }}</span>
                <div class="progress-bar">
                  <div 
                    class="progress-fill" 
                    :style="{ width: `${user.learning_stats?.completion_rate || 0}%` }"
                  ></div>
                </div>
              </div>
            </td>
            <td>
              <span class="completion-rate" :class="getCompletionClass(user.learning_stats?.completion_rate)">
                {{ user.learning_stats?.completion_rate || 0 }}%
              </span>
            </td>
            <td>
              <div class="action-buttons">
                <button @click="viewUserDetail(user)" class="btn btn-sm btn-primary">
                  {{ $t('admin.users.actions.viewDetails') }}
                </button>
                <button @click="resetUserProgress(user)" class="btn btn-sm btn-warning">
                  {{ $t('admin.users.actions.resetProgress') }}
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 分页 -->
    <div class="pagination" v-if="pagination">
      <button 
        @click="loadUsers(pagination.page - 1)" 
        :disabled="pagination.page <= 1"
        class="btn btn-secondary"
      >
        上一页
      </button>
      <span class="page-info">
        第 {{ pagination.page }} 页，共 {{ pagination.pages }} 页
      </span>
      <button 
        @click="loadUsers(pagination.page + 1)" 
        :disabled="pagination.page >= pagination.pages"
        class="btn btn-secondary"
      >
        下一页
      </button>
    </div>

    <!-- 用户详情模态框 -->
    <div v-if="selectedUser" class="modal-overlay" @click="selectedUser = null">
      <div class="modal" @click.stop>
        <div class="modal-header">
          <h3>用户详情</h3>
          <button @click="selectedUser = null" class="close-btn">✕</button>
        </div>
        <div class="modal-body">
          <div class="user-details">
            <div class="detail-row">
              <label>用户ID:</label>
              <span>{{ selectedUser.user_id }}</span>
            </div>
            <div class="detail-row">
              <label>用户名:</label>
              <span>{{ selectedUser.username }}</span>
            </div>
            <div class="detail-row">
              <label>邮箱:</label>
              <span>{{ selectedUser.email }}</span>
            </div>
            <div class="detail-row">
              <label>注册时间:</label>
              <span>{{ formatDate(selectedUser.created_at) }}</span>
            </div>
            <div class="detail-row">
              <label>已完成课程:</label>
              <span>{{ selectedUser.learning_stats?.completed_lessons || 0 }} / {{ selectedUser.learning_stats?.total_lessons || 0 }}</span>
            </div>
            <div class="detail-row">
              <label>完成率:</label>
              <span>{{ selectedUser.learning_stats?.completion_rate || 0 }}%</span>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button @click="selectedUser = null" class="btn btn-secondary">关闭</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { adminAPI } from '../adminApi.js'

export default {
  name: 'AdminUsers',
  setup() {
    const { t } = useI18n()
    const users = ref([])
    const pagination = ref(null)
    const selectedUser = ref(null)
    const loading = ref(false)

    const loadUsers = async (page = 1) => {
      try {
        loading.value = true
        window.setLoading?.(true)
        
        const data = await adminAPI.getUsers({ page, per_page: 20 })
        users.value = data.users
        pagination.value = data.pagination
      } catch (error) {
        console.error('加载用户列表失败:', error)
        window.showNotification?.(t('admin.users.loadUsersError', '加载用户列表失败'), 'error')
      } finally {
        loading.value = false
        window.setLoading?.(false)
      }
    }

    const viewUserDetail = (user) => {
      selectedUser.value = user
    }

    const resetUserProgress = async (user) => {
      if (!confirm(`确定要重置用户 ${user.username} 的学习进度吗？此操作不可撤销，将删除该用户的所有学习进度和代码提交记录。`)) {
        return
      }

      try {
        loading.value = true
        window.setLoading?.(true)
        
        const result = await adminAPI.resetUserProgress(user.user_id)
        
        window.showNotification(
          t('admin.users.resetProgressSuccess', `成功重置用户 ${user.username} 的学习进度，重置了 ${result.details?.reset_lessons || 0} 个课程进度，删除了 ${result.details?.deleted_submissions || 0} 条提交记录`),
          'success'
        )
        
        // 刷新用户列表
        await loadUsers()
        
      } catch (error) {
        console.error('重置用户进度失败:', error)
        const message = error.response?.data?.error || t('admin.users.resetProgressError', '重置用户进度失败')
        window.showNotification?.(message, 'error')
      } finally {
        loading.value = false
        window.setLoading?.(false)
      }
    }

    const formatDate = (dateString) => {
      if (!dateString) return '-'
      return new Date(dateString).toLocaleString('zh-CN')
    }

    const getCompletionClass = (rate) => {
      if (rate >= 80) return 'high'
      if (rate >= 50) return 'medium'
      return 'low'
    }

    onMounted(() => {
      loadUsers()
    })

    return {
      users,
      pagination,
      selectedUser,
      loading,
      loadUsers,
      viewUserDetail,
      resetUserProgress,
      formatDate,
      getCompletionClass,
      t
    }
  }
}
</script>

<style scoped>
.admin-users {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.page-header h1 {
  color: var(--color-slate-800);
  margin: 0;
}

.header-stats {
  display: flex;
  gap: 20px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 24px;
  font-weight: bold;
  color: var(--color-blue-600);
}

.stat-label {
  font-size: 12px;
  color: var(--color-slate-600);
}

.users-table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  overflow: hidden;
  margin-bottom: 20px;
}

.users-table {
  width: 100%;
  border-collapse: collapse;
}

.users-table th {
  background: var(--color-slate-50);
  padding: 16px;
  text-align: left;
  font-weight: 600;
  color: var(--color-slate-800);
  border-bottom: 2px solid var(--color-slate-200);
}

.users-table td {
  padding: 16px;
  border-bottom: 1px solid var(--color-slate-200);
}

.user-row:hover {
  background: var(--color-slate-50);
}

.user-info {
  display: flex;
  align-items: center;
}

.username {
  font-weight: 500;
  color: var(--color-slate-800);
}

.progress-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.progress-bar {
  width: 100px;
  height: 8px;
  background: var(--color-slate-200);
  border-radius: 6px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-blue-600), var(--color-green-600));
  transition: width 0.3s ease;
}

.completion-rate {
  font-weight: bold;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.completion-rate.high {
  background: var(--color-green-50);
  color: var(--color-green-800);
}

.completion-rate.medium {
  background: var(--color-amber-50);
  color: var(--color-amber-800);
}

.completion-rate.low {
  background: var(--color-red-50);
  color: var(--color-red-800);
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin-top: 20px;
}

.page-info {
  color: var(--color-slate-600);
  font-size: 14px;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0,0,0,0.3);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid var(--color-slate-200);
}

.modal-header h3 {
  margin: 0;
  color: var(--color-slate-800);
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  color: var(--color-slate-600);
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.close-btn:hover {
  background: var(--color-slate-50);
  color: var(--color-slate-800);
}

.modal-body {
  padding: 20px;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid var(--color-slate-50);
}

.detail-row label {
  font-weight: 500;
  color: var(--color-slate-600);
}

.detail-row span {
  color: var(--color-slate-800);
}

.modal-footer {
  padding: 20px;
  border-top: 1px solid var(--color-slate-200);
  display: flex;
  justify-content: flex-end;
}

/* 按钮样式 */
.btn {
  padding: 0.75rem 1.25rem;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  border: 1px solid transparent;
  font-size: 0.875rem;
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: white;
  color: var(--color-blue-600);
  border-color: var(--color-blue-200);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
}

.btn-primary:hover:not(:disabled) {
  background: var(--color-blue-50);
  border-color: var(--color-blue-300);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  color: var(--color-blue-700);
}

.btn-secondary {
  background: white;
  color: var(--color-slate-600);
  border-color: var(--color-slate-200);
  box-shadow: 0 2px 4px rgba(148, 163, 184, 0.1);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--color-slate-50);
  border-color: var(--color-slate-300);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(148, 163, 184, 0.15);
  color: var(--color-slate-700);
}

.btn-warning {
  background: white;
  color: var(--color-amber-600);
  border-color: var(--color-amber-200);
  box-shadow: 0 2px 4px rgba(245, 158, 11, 0.1);
}

.btn-warning:hover:not(:disabled) {
  background: var(--color-amber-50);
  border-color: var(--color-amber-300);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.15);
  color: var(--color-amber-700);
}

.btn-sm {
  padding: 0.5rem 0.875rem;
  font-size: 0.75rem;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}
</style>