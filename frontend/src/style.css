/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* 现代色彩系统 - 低饱和度 */
  --color-slate-50: #f8fafc;
  --color-slate-100: #f1f5f9;
  --color-slate-200: #e2e8f0;
  --color-slate-300: #cbd5e1;
  --color-slate-400: #94a3b8;
  --color-slate-500: #64748b;
  --color-slate-600: #475569;
  --color-slate-700: #334155;
  --color-slate-800: #1e293b;
  --color-slate-900: #0f172a;
  
  /* 功能色彩 - 更加subtle */
  --color-blue-50: #eff6ff;
  --color-blue-100: #dbeafe;
  --color-blue-500: #3b82f6;
  --color-blue-600: #2563eb;
  --color-blue-700: #1d4ed8;
  
  --color-green-50: #f0fdf4;
  --color-green-100: #dcfce7;
  --color-green-500: #22c55e;
  --color-green-600: #16a34a;
  
  --color-red-50: #fef2f2;
  --color-red-100: #fee2e2;
  --color-red-500: #ef4444;
  --color-red-600: #dc2626;
  
  --color-amber-50: #fffbeb;
  --color-amber-100: #fef3c7;
  --color-amber-500: #f59e0b;
  --color-amber-600: #d97706;
  
  --color-purple-500: #8b5cf6;
  --color-purple-600: #7c3aed;
  
  --color-indigo-50: #eef2ff;
  --color-indigo-100: #e0e7ff;
  --color-indigo-200: #c7d2fe;
  --color-indigo-300: #a5b4fc;
  --color-indigo-400: #818cf8;
  --color-indigo-500: #6366f1;
  --color-indigo-600: #4f46e5;
  --color-indigo-700: #4338ca;
  --color-indigo-800: #3730a3;
  --color-indigo-900: #312e81;
  
  /* 间距系统 */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;
  
  /* 圆角系统 */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  
  /* 阴影系统 */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  
  /* 图标尺寸系统 */
  --icon-xs: 1rem;    /* 16px - 小图标 */
  --icon-sm: 1.25rem; /* 20px - 中小图标 */
  --icon-md: 1.5rem;  /* 24px - 中等图标 */
  --icon-lg: 2rem;    /* 32px - 大图标 */
  --icon-xl: 3rem;    /* 48px - 超大图标 */
  --icon-2xl: 4rem;   /* 64px - 巨大图标 */
}

body {
  font-family: 'Inter', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', sans-serif;
  background-color: var(--color-slate-50);
  min-height: 100vh;
  color: var(--color-slate-900);
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 导航栏样式 */
.navbar {
  background: white;
  border-bottom: 1px solid var(--color-slate-200);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  backdrop-filter: blur(8px);
  background-color: rgba(255, 255, 255, 0.95);
}

.nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--spacing-6);
  height: 64px;
}

.nav-brand h2 {
  color: var(--color-slate-900);
  font-size: 1.25rem;
  font-weight: 600;
  letter-spacing: -0.025em;
}

.nav-brand .brand-link {
  text-decoration: none;
  color: inherit;
  transition: opacity 0.2s ease;
}

.nav-brand .brand-link:hover {
  opacity: 0.8;
}

.nav-links {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

.nav-link {
  text-decoration: none;
  color: var(--color-slate-600);
  font-weight: 500;
  font-size: 0.875rem;
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-md);
  transition: all 0.15s ease;
  position: relative;
}

.nav-link:hover {
  color: var(--color-slate-900);
  background-color: var(--color-slate-100);
}

.nav-link.router-link-active {
  color: var(--color-blue-600);
  background-color: var(--color-blue-50);
}

.admin-link {
  color: var(--color-amber-600) !important;
}

.admin-link:hover {
  background-color: var(--color-amber-50) !important;
}

.logout-btn {
  background: transparent;
  color: var(--color-slate-600);
  border: 1px solid var(--color-slate-300);
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-md);
  cursor: pointer;
  font-weight: 500;
  font-size: 0.875rem;
  transition: all 0.15s ease;
  margin-left: var(--spacing-2);
}

.logout-btn:hover {
  background: var(--color-red-50);
  color: var(--color-red-600);
  border-color: var(--color-red-200);
}

/* 主要内容区域 */
.main-content {
  min-height: 100vh;
  padding: var(--spacing-6);
}

.main-content.with-nav {
  margin-top: 64px;
  min-height: calc(100vh - 64px);
}

/* 卡片样式 */
.card {
  background: white;
  border-radius: var(--radius-xl);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-slate-200);
  margin-bottom: var(--spacing-6);
  transition: all 0.15s ease;
}

.card:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--color-slate-300);
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-slate-900);
  margin-bottom: var(--spacing-4);
  letter-spacing: -0.025em;
}

/* 表单样式 */
.form-group {
  margin-bottom: var(--spacing-5);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-2);
  font-weight: 500;
  font-size: 0.875rem;
  color: var(--color-slate-700);
}

.form-input {
  width: 100%;
  padding: var(--spacing-3) var(--spacing-4);
  border: 1px solid var(--color-slate-300);
  border-radius: var(--radius-lg);
  font-size: 0.875rem;
  transition: all 0.15s ease;
  background-color: white;
}

.form-input:focus {
  outline: none;
  border-color: var(--color-blue-500);
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

.form-input:hover {
  border-color: var(--color-slate-400);
}

.form-textarea {
  width: 100%;
  padding: var(--spacing-3) var(--spacing-4);
  border: 1px solid var(--color-slate-300);
  border-radius: var(--radius-lg);
  font-size: 0.875rem;
  resize: vertical;
  min-height: 100px;
  transition: all 0.15s ease;
  background-color: white;
  font-family: inherit;
}

.form-textarea:focus {
  outline: none;
  border-color: var(--color-blue-500);
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

.form-textarea:hover {
  border-color: var(--color-slate-400);
}

/* 现代按钮样式系统 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-4);
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1;
  border-radius: var(--radius-lg);
  transition: all 0.15s ease;
  cursor: pointer;
  text-decoration: none;
  white-space: nowrap;
  user-select: none;
  position: relative;
  overflow: hidden;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.btn-sm {
  padding: var(--spacing-2) var(--spacing-3);
  font-size: 0.8125rem;
}

.btn-lg {
  padding: var(--spacing-4) var(--spacing-6);
  font-size: 1rem;
}

/* Primary - 现代蓝色，更subtle */
.btn-primary {
  background: var(--color-blue-600);
  color: white;
  border: 1px solid var(--color-blue-600);
}

.btn-primary:hover:not(:disabled) {
  background: var(--color-blue-700);
  border-color: var(--color-blue-700);
}

/* Secondary - outline风格 */
.btn-secondary {
  background: transparent;
  color: var(--color-slate-700);
  border: 1px solid var(--color-slate-300);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--color-slate-100);
  border-color: var(--color-slate-400);
}

/* Ghost - 无边框 */
.btn-ghost {
  background: transparent;
  color: var(--color-slate-600);
  border: 1px solid transparent;
}

.btn-ghost:hover:not(:disabled) {
  background: var(--color-slate-100);
  color: var(--color-slate-900);
}

/* Success - outline优先 */
.btn-success {
  background: transparent;
  color: var(--color-green-600);
  border: 1px solid var(--color-green-600);
}

.btn-success:hover:not(:disabled) {
  background: var(--color-green-50);
}

.btn-success.btn-solid {
  background: var(--color-green-600);
  color: white;
}

.btn-success.btn-solid:hover:not(:disabled) {
  background: var(--color-green-700);
}

/* Warning */
.btn-warning {
  background: transparent;
  color: var(--color-amber-600);
  border: 1px solid var(--color-amber-600);
}

.btn-warning:hover:not(:disabled) {
  background: var(--color-amber-50);
}

.btn-warning.btn-solid {
  background: var(--color-amber-500);
  color: white;
}

.btn-warning.btn-solid:hover:not(:disabled) {
  background: var(--color-amber-600);
}

/* Danger */
.btn-danger {
  background: transparent;
  color: var(--color-red-600);
  border: 1px solid var(--color-red-600);
}

.btn-danger:hover:not(:disabled) {
  background: var(--color-red-50);
}

.btn-danger.btn-solid {
  background: var(--color-red-500);
  color: white;
}

.btn-danger.btn-solid:hover:not(:disabled) {
  background: var(--color-red-600);
}

/* 加载指示器 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(4px);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--color-slate-300);
  border-top: 3px solid var(--color-blue-600);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-3);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-overlay p {
  color: white;
  font-size: 0.875rem;
  font-weight: 500;
}

/* 统一图标尺寸类 */
.icon {
  width: var(--icon-md);
  height: var(--icon-md);
  flex-shrink: 0;
}

.icon-xs {
  width: var(--icon-xs);
  height: var(--icon-xs);
}

.icon-sm {
  width: var(--icon-sm);
  height: var(--icon-sm);
}

.icon-md {
  width: var(--icon-md);
  height: var(--icon-md);
}

.icon-lg {
  width: var(--icon-lg);
  height: var(--icon-lg);
}

.icon-xl {
  width: var(--icon-xl);
  height: var(--icon-xl);
}

.icon-2xl {
  width: var(--icon-2xl);
  height: var(--icon-2xl);
}

/* 统一图标容器样式 */
.stat-icon {
  width: var(--icon-2xl);
  height: var(--icon-2xl);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-xl);
  transition: all 0.3s ease;
  position: relative;
}

.stat-icon::before {
  content: '';
  width: var(--icon-lg);
  height: var(--icon-lg);
  border-radius: var(--radius-md);
}

.activity-icon {
  width: var(--icon-xl);
  height: var(--icon-xl);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s ease;
}

.activity-icon-inner {
  width: var(--icon-md);
  height: var(--icon-md);
  border-radius: var(--radius-sm);
}

.analysis-icon {
  width: var(--icon-xl);
  height: var(--icon-xl);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  position: relative;
}

.analysis-icon::before {
  content: '';
  width: var(--icon-md);
  height: var(--icon-md);
  border-radius: var(--radius-sm);
}

/* 通知样式 */
.notification {
  position: fixed;
  top: 84px;
  right: var(--spacing-6);
  padding: var(--spacing-4) var(--spacing-5);
  border-radius: var(--radius-xl);
  font-weight: 500;
  font-size: 0.875rem;
  z-index: 9999;
  animation: slideIn 0.3s ease;
  max-width: 400px;
  border: 1px solid;
  backdrop-filter: blur(8px);
}

.notification.success {
  background: var(--color-green-50);
  color: var(--color-green-800);
  border-color: var(--color-green-200);
}

.notification.error {
  background: var(--color-red-50);
  color: var(--color-red-800);
  border-color: var(--color-red-200);
}

.notification.warning {
  background: var(--color-amber-50);
  color: var(--color-amber-800);
  border-color: var(--color-amber-200);
}

.notification.info {
  background: var(--color-blue-50);
  color: var(--color-blue-800);
  border-color: var(--color-blue-200);
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 认证页面样式 */
.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: var(--spacing-6);
  background: var(--color-slate-50);
}

.auth-card {
  background: white;
  border-radius: var(--radius-xl);
  padding: var(--spacing-10);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--color-slate-200);
  width: 100%;
  max-width: 400px;
}

.auth-title {
  text-align: center;
  margin-bottom: var(--spacing-8);
  color: var(--color-slate-900);
  font-size: 1.875rem;
  font-weight: 700;
  letter-spacing: -0.025em;
}

.auth-link {
  text-align: center;
  margin-top: var(--spacing-6);
  color: var(--color-slate-600);
  font-size: 0.875rem;
}

.auth-link a {
  color: var(--color-blue-600);
  text-decoration: none;
  font-weight: 500;
}

.auth-link a:hover {
  color: var(--color-blue-700);
  text-decoration: underline;
}

/* 代码编辑器样式优化 */
.code-textarea {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  tab-size: 2;
  -moz-tab-size: 2;
}

/* 代码语法高亮（简化版） */
.markdown-content pre code {
  display: block;
  overflow-x: auto;
  padding: 0;
}

.markdown-content code {
  color: #e83e8c;
}

/* 提示框样式 */
.hint-box {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
  border-left: 4px solid #fdcb6e;
}

.hint-box h4 {
  color: #856404;
  margin-bottom: 8px;
}

/* 成功/失败状态样式 */
.status-success {
  background: var(--color-green-50);
  color: var(--color-green-800);
  border: 1px solid var(--color-green-200);
  border-radius: var(--radius-lg);
  padding: var(--spacing-4);
}

.status-error {
  background: var(--color-red-50);
  color: var(--color-red-800);
  border: 1px solid var(--color-red-200);
  border-radius: var(--radius-lg);
  padding: var(--spacing-4);
}

.status-warning {
  background: var(--color-amber-50);
  color: var(--color-amber-800);
  border: 1px solid var(--color-amber-200);
  border-radius: var(--radius-lg);
  padding: var(--spacing-4);
}

/* 进度条样式 */
.progress-container {
  margin: var(--spacing-4) 0;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: var(--color-slate-200);
  border-radius: var(--radius-sm);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--color-blue-600);
  transition: width 0.5s ease;
  border-radius: var(--radius-sm);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-container {
    padding: 0 var(--spacing-4);
  }
  
  .nav-links {
    gap: var(--spacing-1);
  }
  
  .nav-link {
    padding: var(--spacing-2);
    font-size: 0.8125rem;
  }
  
  .main-content {
    padding: var(--spacing-4);
  }
  
  .card {
    padding: var(--spacing-5);
  }
  
  .auth-card {
    padding: var(--spacing-8) var(--spacing-5);
  }
  
  .btn {
    padding: var(--spacing-3);
    font-size: 0.8125rem;
  }
  
  .notification {
    right: var(--spacing-4);
    left: var(--spacing-4);
    max-width: none;
  }
}

@media (max-width: 480px) {
  .nav-brand h2 {
    font-size: 1.2rem;
  }
  
  .auth-title {
    font-size: 1.5rem;
  }
  
  .main-content {
    padding: 10px;
  }
  
  .lesson-nav {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}

/* 滚动条样式优化 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 选择文本样式 */
::selection {
  background: #3498db;
  color: white;
}

::-moz-selection {
  background: #3498db;
  color: white;
}