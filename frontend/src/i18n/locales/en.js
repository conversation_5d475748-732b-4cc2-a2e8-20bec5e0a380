export default {
  // Page titles
  title: {
    main: 'Interactive Python Learning Platform',
    home: 'Home - Python Learning Platform',
    login: 'Login - Python Learning Platform',
    register: 'Register - Python Learning Platform',
    dashboard: 'Dashboard - Python Learning Platform',
    modules: 'Course Modules - Python Learning Platform',
    lesson: 'Course Learning - Python Learning Platform',
    admin: 'Admin Panel - Python Learning Platform',
    analytics: 'Data Analytics - Python Learning Platform'
  },

  // Navigation & Common
  navigation: {
    brand: 'Python Learning Platform',
    dashboard: 'Dashboard',
    courses: 'Courses',
    admin: 'Admin Panel',
    research: 'Research Analytics',
    logout: 'Logout',
    home: 'Home'
  },

  // Common UI elements
  common: {
    loading: 'Loading...',
    submit: 'Submit',
    cancel: 'Cancel',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    create: 'Create',
    update: 'Update',
    confirm: 'Confirm',
    back: 'Back',
    next: 'Next',
    previous: 'Previous',
    close: 'Close',
    search: 'Search',
    filter: 'Filter',
    clear: 'Clear',
    refresh: 'Refresh',
    success: 'Success',
    error: 'Error',
    warning: 'Warning',
    info: 'Information'
  },

  // Home page
  home: {
    title: 'Interactive Python Learning Platform',
    subtitle: 'Master Python programming with AI-powered guidance and real-time feedback',
    nav: {
      brand: 'Python Learning Platform',
      studentLogin: 'Student Login',
      register: 'Register',
      admin: 'Admin',
      dashboard: 'Dashboard',
      adminDashboard: 'Admin Dashboard'
    },
    hero: {
      title: 'Start Your',
      titleHighlight: 'Python Programming',
      titleEnd: 'Journey',
      subtitle: 'Online Python learning platform designed for programming beginners',
      subtitleLine2: 'No environment setup required, write and run instantly, get immediate feedback',
      startLearning: 'Start Learning for Free',
      hasAccount: 'Already have an account? Login',
      continueLearning: 'Continue Learning',
      goToAdminDashboard: 'Go to Admin Dashboard'
    },
    codePreview: {
      comment1: '# Your first Python program',
      comment2: '# Variables and data types',
      learnerName: 'Python Learner',
      ageOutput: 'I am {name}, {age} years old',
      comment3: '# Conditional statements',
      adultMessage: 'I am an adult!',
      childMessage: 'I am still a child'
    },
    features: {
      title: 'Why Choose Our Platform?',
      zeroSetup: {
        title: 'Zero Environment Setup',
        description: 'No need to install Python or any development tools, just open your browser and start programming.'
      },
      instantFeedback: {
        title: 'Instant Feedback',
        description: 'Code execution results and error messages are displayed immediately, helping you quickly find and fix problems.'
      },
      personalizedLearning: {
        title: 'Personalized Learning',
        description: 'Based on your learning progress and ability, the system will recommend suitable course content and exercises.'
      },
      progressiveTeaching: {
        title: 'Progressive Teaching',
        description: 'From basic syntax to advanced concepts, step-by-step course design makes learning easier.'
      },
      smartHints: {
        title: 'Smart Hints',
        description: 'When you encounter difficulties, the system provides layered hints to guide you to solutions.'
      },
      learningTracking: {
        title: 'Learning Tracking',
        description: 'Detailed learning progress statistics help you clearly understand your learning achievements.'
      }
    },
    learningPath: {
      title: 'Learning Path',
      step1: {
        title: 'Programming Basics',
        description: 'Understand what programming is, learn Python basic syntax and data types'
      },
      step2: {
        title: 'Data Processing',
        description: 'Learn strings, numerical operations, and user input handling'
      },
      step3: {
        title: 'Logic Control',
        description: 'Master conditional statements, loop structures, and list operations'
      },
      step4: {
        title: 'Code Organization',
        description: 'Learn function definitions, parameter passing, and return values'
      }
    },
    cta: {
      title: 'Ready to start your programming journey?',
      subtitle: 'Join us and start your free Python learning experience',
      registerNow: 'Register Now'
    },
    footer: {
      brand: 'Python Learning Platform',
      brandSubtitle: 'Online learning platform designed for programming beginners',
      quickLinks: 'Quick Links',
      studentLogin: 'Student Login',
      registerAccount: 'Register Account',
      adminLogin: 'Admin Login',
      dashboard: 'Student Dashboard',
      adminDashboard: 'Admin Dashboard',
      learningSupport: 'Learning Support',
      userGuide: 'User Guide',
      faq: 'FAQ',
      contactUs: 'Contact Us',
      copyright: '© 2024 Python Learning Platform. All rights reserved.'
    }
  },

  // Authentication
  auth: {
    login: 'Login',
    register: 'Register',
    username: 'Username',
    password: 'Password',
    email: 'Email',
    confirmPassword: 'Confirm Password',
    loginTitle: 'Welcome Back',
    usernamePlaceholder: 'Enter your username',
    passwordPlaceholder: 'Enter your password',
    loggingIn: 'Logging in...',
    loginError: 'Login failed, please check your username and password',
    emailPlaceholder: 'Enter your email address',
    confirmPasswordPlaceholder: 'Enter your password again',
    registering: 'Registering...',
    registerError: 'Registration failed, please try again later',
    loginSubtitle: 'Sign in to continue your Python learning journey',
    registerTitle: 'Join Our Community',
    registerSubtitle: 'Create your account to start learning Python',
    loginButton: 'Sign In',
    registerButton: 'Create Account',
    forgotPassword: 'Forgot Password?',
    noAccount: "Don't have an account?",
    haveAccount: 'Already have an account?',
    loginSuccess: 'Login successful!',
    registerSuccess: 'Registration successful! Welcome to the Python learning journey!',
    logoutSuccess: 'Logged out successfully',
    invalidCredentials: 'Invalid username or password',
    passwordMismatch: 'Passwords do not match',
    usernameTaken: 'Username already taken',
    emailTaken: 'Email already registered'
  },

  // Dashboard
  dashboard: {
    title: 'Learning Dashboard',
    welcome: 'Welcome back, {username}! Continue your Python learning journey!',
    logout: 'Logout',
    stats: {
      totalLessons: 'Total Lessons',
      completedLessons: 'Completed',
      inProgressLessons: 'In Progress',
      completionRate: 'Completion Rate',
      averageScore: 'Average Score',
      hintsUsed: 'Hints Used',
      exercisesNeedingHints: '{count} exercises need hints',
      timeSpent: 'Time Spent',
      totalSubmissions: 'Submissions',
      successRate: 'Success Rate'
    },
    progress: {
      title: 'Overall Learning Progress',
      lessonsCompleted: '{completed} / {total} lessons completed'
    },
    recentActivity: {
      title: 'Recent Activity',
      noActivity: 'No recent activity',
      completed: 'Completed',
      started: 'Started',
      submitted: 'Submitted code for',
      exerciseCompleted: 'Successfully completed exercise',
      exerciseAttempt: 'Exercise attempt'
    },
    codeQuality: {
      title: 'Code Quality Analysis',
      averageLines: 'Average Lines of Code',
      complexity: 'Average Complexity',
      syntaxScore: 'Code Quality Score',
      recentImprovement: 'Recent Improvement'
    },
    hintAnalysis: {
      title: 'Hint Usage Analysis',
      totalHints: 'Total Hints Used',
      exercisesWithHints: 'Exercises with Hints',
      level: 'Level',
      mostUsedLevel: 'Most Used Level',
      recentTrend: 'Recent Trend',
      levelDistribution: 'Hint Level Distribution'
    },
    errorPatterns: {
      title: 'Learning Analysis',
      detailedErrorAnalysis: 'Detailed Error Analysis',
      syntaxError: 'Syntax Errors',
      nameError: 'Name Errors',
      typeError: 'Type Errors',
      runtimeError: 'Runtime Errors',
      logicError: 'Logic Errors',
      timeoutError: 'Timeout Errors',
      noError: 'No Errors',
      times: 'times'
    },
    quickActions: {
      title: 'Quick Actions',
      continueLastLesson: 'Continue Learning',
      browseModules: 'Browse Courses',
      viewProgress: 'View Progress',
      refreshData: 'Refresh Data'
    },
    confirmLogout: 'Are you sure you want to logout?',
    loadDashboardError: 'Failed to load dashboard data',
    loadCodeAnalysisError: 'Failed to load code analysis data'
  },

  // 🔬 NEW: Research Analytics Page
  research: {
    title: 'Educational Technology Research Analytics',
    subtitle: 'Comparative Study of AI Feedback Effectiveness in Intelligent Tutoring Systems',
    academicBadge: 'MSc Computer Science Dissertation Research',
    loading: 'Loading research data...',
    error: 'Failed to load research data',
    loadError: 'Unable to connect to research data API',
    retry: 'Retry',
    
    overview: {
      title: 'Research Data Overview',
      totalFeedback: 'Total Feedback Instances',
      totalRecords: 'Effectiveness Records',
      successfulCases: 'Successful Cases',
      successRate: 'Overall Success Rate'
    },
    
    question: {
      title: 'Core Research Questions',
      main: 'Research Question:',
      methodology: 'Methodology:',
      period: 'Data Collection Period:'
    },
    
    matrix: {
      title: 'AI Feedback Effectiveness Comparison Matrix',
      description: 'The following matrix displays the average success time, attempt counts, and user ratings for different AI feedback types when handling various programming errors. This is the core data visualization of this research.',
      attempts: 'attempts',
      noData: 'No Data',
      legend: 'Legend (classified by average success time)',
      excellent: 'Excellent',
      good: 'Good',
      moderate: 'Moderate',
      poor: 'Poor',
      preparing: 'Matrix data is being prepared...',
      reload: 'Reload Data'
    },
    
    feedbackTypes: {
      structural: 'Technical Analysis',
      openai: 'OpenAI Educational',
      deepseek: 'DeepSeek Educational',
      localRules: 'Local Rules'
    },
    
    errorTypes: {
      syntax: 'Syntax Error',
      name: 'Name Error',
      type: 'Type Error',
      logic: 'Logic Error',
      runtime: 'Runtime Error',
      noError: 'No Error'
    },
    
    insights: {
      title: 'Research Findings & Academic Value',
      dataIntegrity: 'Data Integrity',
      dataIntegrityDesc: 'The system successfully implements a complete data loop from feedback generation to effectiveness tracking, providing a reliable data foundation for educational technology research.',
      feedbackComparison: 'Feedback Comparison Analysis',
      feedbackComparisonDesc: 'Through quantitative analysis of different AI model feedback effectiveness, this research provides empirical evidence for optimizing feedback strategies in intelligent tutoring systems.',
      userExperience: 'User Experience Evaluation',
      userExperienceDesc: 'Combining quantitative data with subjective user ratings forms a multi-dimensional feedback effectiveness evaluation framework, enhancing the academic rigor of the research.'
    },
    
    export: {
      title: 'Data Export & Academic Citation',
      csv: 'Export CSV Format',
      json: 'Export JSON Format',
      citation: 'Generate Citation',
      citationFormat: 'Suggested Citation Format:'
    }
  },

  // Modules (Courses)
  modules: {
    title: 'Python Course Modules',
    subtitle: 'Choose a module to start learning, complete courses in order for the best learning experience',
    progress: 'Progress',
    lessons: 'Lessons',
    exercises: 'Exercises',
    startModule: 'Start Module',
    continueModule: 'Continue Module',
    moduleCompleted: 'Module Completed!',
    lessonsCompleted: 'lessons completed',
    prerequisiteRequired: 'Complete prerequisite lessons first',
    loading: 'Loading courses...',
    emptyTitle: 'No course content available',
    emptyDescription: 'Course content is being prepared, please try again later',
    reload: 'Reload',
    loadError: 'Failed to load courses, please try again later',
    prerequisiteWarning: 'Please complete prerequisite lessons first',
    progressStatus: {
      notStarted: 'Not started',
      inProgress: 'In progress ({attempts} attempts)',
      completed: 'Completed',
      unknown: 'Unknown status'
    },
    lessonStatus: {
      locked: 'Locked',
      available: 'Available',
      inProgress: 'In Progress',
      completed: 'Completed'
    },
    noModules: 'No modules available yet'
  },

  // Lesson
  lesson: {
    title: 'Lesson',
    exercise: 'Exercise',
    content: 'Content',
    practice: 'Practice',
    runCode: 'Run Code',
    submitCode: 'Submit Code',
    resetCode: 'Reset Code',
    getHint: 'Get Hint',
    showHints: 'Show Hints',
    hideHints: 'Hide Hints',
    testCases: 'Test Cases',
    expectedOutput: 'Expected Output',
    yourOutput: 'Your Output',
    feedback: 'Feedback',
    hints: 'Hints',
    problemStatement: 'Problem Statement',
    instructions: 'Instructions',
    codeEditor: 'Code Editor',
    placeholder: 'Write your Python code here...',
    toggleTheme: 'Toggle Theme',
    lightTheme: 'Light Theme',
    darkTheme: 'Dark Theme',
    simpleView: 'Simple View',
    detailedView: 'Detailed View',
    runSuccess: 'Code executed successfully!',
    submitSuccess: 'Code submitted successfully!',
    lessonCompleted: 'Lesson completed! Well done!',
    nextLesson: 'Next Lesson',
    previousLesson: 'Previous Lesson',
    backToModules: 'Back to Modules',
    codeAnalysis: 'Code Analysis',
    executionTime: 'Execution Time',
    codeLines: 'Lines of Code',
    complexity: 'Complexity',
    // New keys for Lesson.vue
    backToCourseList: 'Back to Course List',
    attemptCount: 'Attempt {count}',
    avgSolvingTime: 'Avg Solving Time',
    programmingExercises: '💻 Programming Exercises',
    exercises: 'exercises',
    exerciseNumber: 'Exercise {number}',
    hintNumber: 'Hint {number}:',
    pythonCodeEditor: 'Python Code Editor',
    running: 'Running...',
    codeCorrect: 'Excellent! Code runs correctly!',
    codeNeedsImprovement: 'Code needs modification',
    viewDetailedComparison: 'View detailed feedback comparison',
    viewComparison: 'Detailed Tutoring',
    simpleMode: 'Simple Explanation',
    errorMessage: 'Error Message:',
    codeQualityAnalysis: '📊 Code Quality Analysis',
    linesOfCode: 'Lines of Code:',
    qualityScore: 'Quality Score:',
    improvementSuggestions: '💡 Improvement Suggestions',
    noSuggestionsAvailable: 'No suggestions available',
    input: 'Input:',
    noInput: '(No input)',
    congratulationsCompleted: '🎉 Congratulations on completing this lesson!',
    masteredAllConcepts: 'You have successfully mastered all the concepts in this lesson and can proceed to the next course.',
    continueToNextCourse: 'Continue to Next Course',
    loadingContent: 'Loading lesson content...',
    loadFailed: '😞 Lesson failed to load',
    loadFailedMessage: 'Unable to load lesson content, please check your network connection or try again later.',
    reload: 'Reload',
    hintReminder: '💡 You can get hints after multiple attempts to help solve the problem',
    viewHistory: 'View History',
    submissionHistory: '📝 Submission History',
    attempt: 'Attempt {number}',
    clickToViewCode: 'Click to view submitted code',
    submissionCodeTitle: '📝 Submission Code Details',
    success: 'Success',
    failed: 'Failed',
    noCodeAvailable: 'No code available',
    copyToEditor: '📋 Copy to Editor',
    feedbackComparison: '💡 Feedback Comparison Analysis',
    feedbackSources: 'Feedback Sources:',
    recommendedPrimaryFeedback: 'Recommended to view first:',
    learningStage: 'Learning Stage:',
    statusLabels: {
      not_started: 'Not Started',
      in_progress: 'In Progress',
      completed: 'Completed',
      unknown: 'Unknown'
    },
    stageLabels: {
      advanced_beginner: '🚀 Fast Learner',
      typical_learner: '📚 Regular Learner',
      needs_support: '🤝 Needs Support',
      unknown: '❓ Unknown Stage'
    },
    feedbackTypeLabels: {
      structural: '🔧 Technical Analysis',
      openai: '🤖 OpenAI Tutor',
      deepseek: '🚀 DeepSeek Tutor',
      local_rules: '📋 Local Rules',
      openai_educational: '🤖 OpenAI Tutor',
      deepseek_educational: '🚀 DeepSeek Tutor'
    },
    errorTypes: {
      syntax_error: 'Syntax Error',
      name_error: 'Name Error',
      type_error: 'Type Error',
      runtime_error: 'Runtime Error',
      logic_error: 'Logic Error',
      timeout_error: 'Timeout Error',
      no_error: 'No Error'
    },
    notifications: {
      enterCodeFirst: 'Please enter code first',
      executionFailed: 'Code execution failed, please try again later',
      codeExecutedCorrectly: '🎉 Code executed correctly!',
      codeNeedsModification: 'Code needs modification, please check feedback',
      hintObtained: 'Obtained hint {level}: {hint}',
      hintRequestFailed: 'Failed to get hint',
      deepseekFeedbackLoaded: 'DeepSeek tutor feedback loaded',
      deepseekFeedbackFailed: 'Failed to load DeepSeek feedback',
      feedbackRatingSuccess: 'Thank you for your rating! This will help us improve feedback quality.',
      feedbackRatingFailed: 'Failed to submit rating, please try again later',
      loadingFailed: 'Failed to load course',
      historyLoaded: '📝 Submission history loaded',
      historyLoadFailed: 'Failed to get submission history',
      codeCopiedToEditor: '📋 Code copied to editor'
    }
  },

  // Feedback
  feedback: {
    title: 'Feedback',
    structural: {
      title: 'Technical Analysis',
      subtitle: 'Detailed technical feedback about your code'
    },
    ai: {
      title: 'AI Tutor Feedback',
      subtitle: 'Educational guidance from AI tutors'
    },
    encouragement: 'Encouragement',
    problemAnalysis: 'Problem Analysis',
    conceptExplanation: 'Concept Explanation',
    learningHints: 'Learning Hints',
    suggestions: 'Learning Suggestions',
    technicalDetails: 'Technical Details',
    noContent: 'No feedback content available',
    rating: {
      title: 'How helpful was this feedback?',
      helpfulness: 'Helpfulness:',
      clarity: 'Clarity:',
      submitRating: 'Submit Rating',
      thankYou: 'Thank you for your feedback!'
    },
    comparison: {
      title: 'Feedback Comparison',
      openai: 'OpenAI GPT-4',
      deepseek: 'DeepSeek',
      structural: 'Technical Analysis'
    },
    requestMore: 'Request Additional Feedback',
    generationTime: 'Generated in',
    cost: 'Cost'
  },

  // Admin section
  admin: {
    title: 'Administration Panel',
    modules: 'Manage Modules',
    lessons: 'Manage Lessons',
    exercises: 'Manage Exercises',
    users: {
      title: 'User Management',
      totalUsers: 'Total Users',
      table: {
        userId: 'User ID',
        username: 'Username',
        email: 'Email',
        registeredAt: 'Registration Date',
        learningProgress: 'Learning Progress',
        completionRate: 'Completion Rate',
        actions: 'Actions'
      },
      actions: {
        viewDetails: 'View Details',
        resetProgress: 'Reset Progress'
      }
    },
    analytics: 'Analytics',
    login: {
      title: 'Admin Login',
      subtitle: 'Access the administration panel',
      platformSubtitle: 'Python Learning Platform - Backend Management System',
      usernamePlaceholder: 'Enter admin username',
      studentLogin: 'Student Login',
      backToStudent: 'Back to Student Portal?',
      adminOnlyError: 'Only administrators can access the backend system'
    },
    dashboard: {
      title: 'Admin Dashboard',
      subtitle: 'Python Learning Platform Management Center',
      contentManagement: 'Content Management',
      contentManagementDesc: 'Manage course modules, course content and exercises',
      userManagement: 'User Management',
      userManagementDesc: 'View student information and learning progress',
      learningAnalytics: 'Learning Analytics',
      recentActivity: 'Recent Activity',
      monthlyNew: 'Monthly new',
      modules: 'modules',
      codeExercises: 'Code exercises',
      submissions: 'submissions',
      userExercise: 'User {userId} - Exercise {exerciseId}',
      stats: {
        totalUsers: 'Registered Students',
        totalLessons: 'Total Lessons',
        totalExercises: 'Exercises',
        totalSubmissions: 'Code Submissions',
        activeUsers: 'Active Users',
        successRate: 'Success Rate'
      },
      recentUsers: 'New Registered Users',
      recentSubmissions: 'Recent Code Submissions'
    },
    modules: {
      title: 'Module Management',
      create: 'Create Module',
      edit: 'Edit Module',
      name: 'Module Name',
      description: 'Description',
      order: 'Order',
      lessonsCount: 'Lessons'
    },
    lessons: {
      title: 'Lesson Management',
      create: 'Create Lesson',
      edit: 'Edit Lesson',
      title_field: 'Lesson Title',
      content: 'Content',
      module: 'Module',
      prerequisite: 'Prerequisite Lesson',
      order: 'Order',
      preview: 'Preview',
      exercisesCount: 'Exercises'
    },
    exercises: {
      title: 'Exercise Management',
      create: 'Create Exercise',
      edit: 'Edit Exercise',
      problemStatement: 'Problem Statement',
      lesson: 'Lesson',
      hints: 'Hints',
      testCases: 'Test Cases',
      input: 'Input',
      expectedOutput: 'Expected Output',
      hidden: 'Hidden Test Case'
    },
    // Extended admin interface translations
    interface: {
      modules: {
        title: '📚 Module Management',
        createNew: '➕ Create New Module',
        edit: 'Edit',
        delete: 'Delete',
        moduleTitle: 'Module Title',
        moduleDescription: 'Module Description',
        displayOrder: 'Display Order',
        editModule: 'Edit Module',
        createNewModule: 'Create New Module',
        moduleManagement: 'Manage Lessons',
        manageLessons: 'Manage Lessons',
        lessonCount: 'lessons',
        order: 'Order',
        cancel: 'Cancel',
        save: 'Save',
        saving: 'Saving...',
        enterModuleTitle: 'Enter module title',
        enterModuleDescription: 'Enter module description',
        moduleUpdateSuccess: 'Module updated successfully',
        moduleCreateSuccess: 'Module created successfully',
        moduleDeleteSuccess: 'Module deleted successfully',
        moduleDeleteConfirm: 'Are you sure you want to delete module "{title}"? This will also delete all lessons under this module.',
        saveFailed: 'Save failed',
        deleteFailed: 'Delete failed',
        loadModulesError: 'Failed to load module list'
      },
      lessons: {
        title: '📖 Lesson Management',
        allModules: 'All Modules',
        createNewLesson: '➕ Create New Lesson',
        edit: 'Edit',
        delete: 'Delete',
        lessonTitle: 'Lesson Title',
        belongsToModule: 'Belongs to Module',
        displayOrder: 'Display Order',
        prerequisiteLesson: 'Prerequisite Lesson',
        lessonContent: 'Lesson Content (Markdown)',
        editLesson: 'Edit Lesson',
        createNewLessonTitle: 'Create New Lesson',
        pleaseSelectModule: 'Please select module',
        enterLessonTitle: 'Enter lesson title',
        noPrerequisite: 'No prerequisite lesson',
        enterContentMarkdown: 'Enter lesson content, supports Markdown syntax',
        markdownToolbar: {
          h1: 'H1',
          h2: 'H2',
          bold: 'B',
          italic: 'I',
          code: 'Code',
          codeBlock: 'Block',
          list: 'List',
          link: 'Link',
          edit: 'Edit',
          preview: 'Preview'
        },
        cancel: 'Cancel',
        save: 'Save',
        saving: 'Saving...',
        lessonUpdateSuccess: 'Lesson updated successfully',
        lessonCreateSuccess: 'Lesson created successfully',
        lessonDeleteSuccess: 'Lesson deleted successfully',
        lessonDeleteConfirm: 'Are you sure you want to delete lesson "{title}"? This will also delete all exercises under this lesson.',
        saveFailed: 'Save failed',
        deleteFailed: 'Delete failed',
        loadLessonsError: 'Failed to load lesson list',
        manageExercises: 'Manage Exercises',
        noContent: 'No content',
        unknownModule: 'Unknown module'
      },
      exercises: {
        title: '💻 Exercise Management',
        allLessons: 'All Lessons',
        createNewExercise: '➕ Create New Exercise',
        loadingExercises: '🔄 Loading exercise data...',
        noExercisesTitle: '📝 No exercise data',
        noExercisesDesc: 'Please create some exercises first, or check network connection',
        exerciseNumber: 'Exercise {id}',
        edit: '✏️ Edit',
        testCases: '🧪 Test Cases',
        delete: '🗑️ Delete',
        testCaseCount: '{count} test cases',
        problemStatement: 'Problem Statement',
        hints: 'Hints',
        editExercise: 'Edit Exercise',
        createNewExerciseTitle: 'Create New Exercise',
        belongsToLesson: 'Belongs to Lesson',
        pleaseSelectLesson: 'Please select lesson',
        enterProblemStatement: 'Enter problem statement and requirements',
        hintsPerLine: 'Hints (one per line)',
        enterHints: 'Enter hint content, one hint per line',
        cancel: 'Cancel',
        save: 'Save',
        saving: 'Saving...',
        exerciseUpdateSuccess: 'Exercise updated successfully',
        exerciseCreateSuccess: 'Exercise created successfully',
        exerciseDeleteSuccess: 'Exercise deleted successfully',
        exerciseDeleteConfirm: 'Are you sure you want to delete exercise {id}? This will also delete all test cases.',
        saveFailed: 'Save failed',
        deleteFailed: 'Delete failed',
        loadExercisesError: 'Failed to load exercise list',
        loadLessonsError: 'Failed to load lessons',
        unknownLesson: 'Unknown lesson',
        testCaseManagement: {
          title: 'Test Case Management',
          exerciseTestCases: 'Test cases for exercise {id}',
          addTestCase: '➕ Add Test Case',
          testCaseNumber: 'Test case {id}',
          edit: '✏️ Edit',
          delete: '🗑️ Delete',
          inputData: 'Input data:',
          expectedOutput: 'Expected output:',
          noInput: '(No input)',
          editTestCase: 'Edit Test Case',
          addTestCaseTitle: 'Add Test Case',
          inputDataLabel: 'Input Data',
          inputDataPlaceholder: 'Input data (leave empty if exercise requires no input)',
          expectedOutputLabel: 'Expected Output',
          expectedOutputPlaceholder: 'Expected output result',
          cancel: 'Cancel',
          save: 'Save',
          saving: 'Saving...',
          testCaseUpdateSuccess: 'Test case updated successfully',
          testCaseCreateSuccess: 'Test case created successfully',
          testCaseDeleteSuccess: 'Test case deleted successfully',
          testCaseDeleteConfirm: 'Are you sure you want to delete test case {id}?',
          saveFailed: 'Save failed',
          deleteFailed: 'Delete failed',
          loadTestCasesError: 'Failed to load test cases',
          visibility: 'Visibility',
          visible: 'Visible to Users',
          hidden: 'Hidden',
          hideFromUsers: 'Hide this test case from users',
          hideFromUsersDesc: 'Hidden test cases will not be displayed to students on the lesson page, but will still be used for code evaluation'
        }
      }
    },
    users: {
      title: 'User Management',
      username: 'Username',
      email: 'Email',
      role: 'Role',
      createdAt: 'Created At',
      progress: 'Progress',
      resetProgress: 'Reset Progress',
      viewDetails: 'View Details',
      totalUsers: 'Total Users'
    },
    analytics: {
      title: 'Learning Analytics',
      overview: 'Overview',
      userActivity: 'User Activity',
      contentStats: 'Content Statistics',
      errorAnalysis: 'Error Analysis',
      performanceMetrics: 'Performance Metrics',
      platformOverview: 'Platform Overview',
      lessonCompletionRate: 'Lesson Completion Rate',
      exerciseSuccessRate: 'Exercise Success Rate',
      errorTypeAnalysis: 'Error Type Analysis',
      noErrorData: 'No error data available',
      platformLearningAnalysis: 'Platform Learning Analysis',
      totalLearners: 'Total Learners',
      avgSuccessRate: 'Average Success Rate',
      avgCodeQuality: 'Average Code Quality',
      activeLearners: 'Active Learners',
      commonErrors: 'Common Error Types',
      exerciseDifficulty: 'Exercise Difficulty Analysis',
      attempts: 'attempts',
      mostActiveUsers: 'Most Active Learners',
      submissions: 'submissions',
      noActiveUserData: 'No active user data available',
      recentActivityTrend: 'Recent Activity Trend',
      last7DaysSubmissions: 'Last 7 days submissions',
      noActivityTrendData: 'No activity trend data available',
      loadAnalyticsError: 'Failed to load analytics data',
      noError: 'No error'
    }
  },

  // Error messages
  errors: {
    networkError: 'Network error. Please check your connection.',
    serverError: 'Server error. Please try again later.',
    validationError: 'Please check your input.',
    unauthorized: 'You are not authorized to perform this action.',
    notFound: 'The requested resource was not found.',
    generalError: 'An unexpected error occurred.',
    codeExecutionError: 'Code execution failed.',
    submissionError: 'Failed to submit code. Please try again.'
  },

  // Success messages
  success: {
    codeSaved: 'Code saved successfully',
    progressUpdated: 'Progress updated',
    lessonCompleted: 'Lesson completed successfully',
    moduleCompleted: 'Module completed successfully',
    profileUpdated: 'Profile updated successfully'
  }
}