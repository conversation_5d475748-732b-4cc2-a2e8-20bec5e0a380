"""
代码分析统计API - 提供更详细的学习分析数据
"""

from flask import Blueprint
from flask_jwt_extended import jwt_required, get_jwt_identity
from models.content_models import Exercise
from models.learning_models import Submission, User, UserRole, HintUsage, FeedbackEffectiveness, FeedbackInstance
from extensions import db
from sqlalchemy import desc
from typing import Dict, <PERSON><PERSON>
from datetime import datetime, timedelta, timezone
import json

analytics_bp = Blueprint('analytics', __name__)

@analytics_bp.route('/user/code_analysis', methods=['GET'])
@jwt_required()
def get_user_code_analysis() -> Tuple[Dict, int]:
    """获取用户的代码分析统计"""
    user_id = int(get_jwt_identity())

    try:
        # 获取用户的所有提交记录
        submissions = Submission.query.filter_by(user_id=user_id).order_by(desc(Submission.timestamp)).all()
        
        if not submissions:
            return {'message': '暂无提交记录'}, 404
        
        # 计算统计信息
        total_submissions = len(submissions)
        correct_submissions = len([s for s in submissions if s.is_correct])
        success_rate = (correct_submissions / total_submissions) * 100 if total_submissions > 0 else 0
        
        # 代码复杂度分析
        complexities = [s.code_complexity for s in submissions if s.code_complexity is not None]
        avg_complexity = sum(complexities) / len(complexities) if complexities else 0
        
        # 代码质量分析
        syntax_scores = [s.syntax_score for s in submissions if s.syntax_score is not None]
        avg_syntax_score = sum(syntax_scores) / len(syntax_scores) if syntax_scores else 0
        
        # 代码行数统计
        code_lines = [s.code_lines for s in submissions if s.code_lines is not None]
        avg_lines = sum(code_lines) / len(code_lines) if code_lines else 0
        
        # 最近的进步趋势 - 修正逻辑
        recent_submissions = submissions[:10]  # 最近10次提交
        recent_success_rate = (len([s for s in recent_submissions if s.is_correct]) / len(recent_submissions)) * 100 if recent_submissions else 0
        
        # 计算早期成功率用于对比
        if len(submissions) >= 20:  # 至少20次提交才能有意义地比较两个10次区间
            early_submissions = submissions[-10:]  # 最早的10次提交
            early_success_rate = (len([s for s in early_submissions if s.is_correct]) / len(early_submissions)) * 100
            improvement = recent_success_rate - early_success_rate
        elif len(submissions) >= 10:
            # 10-19次提交：比较最近一半 vs 早期一半
            mid_point = len(submissions) // 2
            recent_half = submissions[:mid_point]
            early_half = submissions[mid_point:]
            recent_half_success_rate = (len([s for s in recent_half if s.is_correct]) / len(recent_half)) * 100 if recent_half else 0
            early_half_success_rate = (len([s for s in early_half if s.is_correct]) / len(early_half)) * 100 if early_half else 0
            improvement = recent_half_success_rate - early_half_success_rate
        else:
            # 少于10次提交：无法计算有意义的进步趋势
            improvement = 0
        
        # 错误类型分析
        error_types = {}
        for submission in submissions:
            if not submission.is_correct and submission.error_type:
                error_type = submission.error_type.value
                error_types[error_type] = error_types.get(error_type, 0) + 1
        
        # HintUsage统计分析
        hint_usage_records = HintUsage.query.filter_by(user_id=user_id).all()
        total_hints_used = len(hint_usage_records)
        
        # 按练习统计提示使用
        hints_by_exercise = {}
        for hint in hint_usage_records:
            exercise_id = hint.exercise_id
            if exercise_id not in hints_by_exercise:
                hints_by_exercise[exercise_id] = {'count': 0, 'max_level': 0}
            hints_by_exercise[exercise_id]['count'] += 1
            hints_by_exercise[exercise_id]['max_level'] = max(hints_by_exercise[exercise_id]['max_level'], hint.hint_level)
        
        # 按提示级别统计
        hints_by_level = {}
        for hint in hint_usage_records:
            level = hint.hint_level
            hints_by_level[level] = hints_by_level.get(level, 0) + 1
        
        # 最近7天提示使用趋势
        seven_days_ago = datetime.now(timezone.utc) - timedelta(days=7)
        recent_hints_by_day = {}
        for hint in hint_usage_records:
            if hint.used_at >= seven_days_ago:
                day_key = hint.used_at.strftime('%Y-%m-%d')
                recent_hints_by_day[day_key] = recent_hints_by_day.get(day_key, 0) + 1
        
        # 时间趋势（最近7天）- 代码提交
        recent_submissions_by_day = {}
        
        for submission in submissions:
            if submission.timestamp >= seven_days_ago:
                day_key = submission.timestamp.strftime('%Y-%m-%d')
                if day_key not in recent_submissions_by_day:
                    recent_submissions_by_day[day_key] = {'total': 0, 'correct': 0}
                recent_submissions_by_day[day_key]['total'] += 1
                if submission.is_correct:
                    recent_submissions_by_day[day_key]['correct'] += 1
        
        return {
            'overall_stats': {
                'total_submissions': total_submissions,
                'correct_submissions': correct_submissions,
                'success_rate': round(success_rate, 2),
                'avg_complexity': round(avg_complexity, 2),
                'avg_syntax_score': round(avg_syntax_score, 2),
                'avg_code_lines': round(avg_lines, 2),
                'total_hints_used': total_hints_used
            },
            'recent_trend': {
                'recent_success_rate': round(recent_success_rate, 2),
                'improvement': round(improvement, 2)
            },
            'error_analysis': error_types,
            'hint_analysis': {
                'total_hints_used': total_hints_used,
                'hints_by_exercise': hints_by_exercise,
                'hints_by_level': hints_by_level,
                'recent_hints_by_day': recent_hints_by_day,
                'exercises_needing_hints': len(hints_by_exercise)
            },
            'daily_activity': recent_submissions_by_day
        }, 200
        
    except Exception as e:
        print(f"❌ Analytics API错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return {'error': f'获取分析数据失败: {str(e)}'}, 500

@analytics_bp.route('/exercise/<int:exercise_id>/analysis', methods=['GET'])
@jwt_required()
def get_exercise_analysis(exercise_id: int) -> Tuple[Dict, int]:
    """获取特定练习的分析数据"""
    user_id = int(get_jwt_identity())
    
    try:
        # 获取练习信息
        exercise = Exercise.query.get(exercise_id)
        if not exercise:
            return {'error': '练习不存在'}, 404
        
        # 获取用户对该练习的提交记录
        submissions = Submission.query.filter_by(
            user_id=user_id,
            exercise_id=exercise_id
        ).order_by(desc(Submission.timestamp)).all()
        
        if not submissions:
            return {'message': '暂无提交记录'}, 404
        
        # 分析提交历史
        submission_history = []
        for submission in submissions:
            api_analysis = None
            if submission.api_analysis_result:
                try:
                    api_analysis = json.loads(submission.api_analysis_result)
                except json.JSONDecodeError:
                    api_analysis = None
            
            submission_history.append({
                'submission_id': submission.submission_id,
                'timestamp': submission.timestamp.isoformat(),
                'is_correct': submission.is_correct,
                'code_lines': submission.code_lines,
                'complexity': submission.code_complexity,
                'syntax_score': submission.syntax_score,
                'error_type': submission.error_type.value if submission.error_type else None,
                'api_analysis': api_analysis
            })
        
        # 计算改进趋势
        if len(submissions) >= 2:
            first_submission = submissions[-1]  # 最早的提交
            latest_submission = submissions[0]   # 最新的提交
            
            improvement = {
                'syntax_score_improvement': (latest_submission.syntax_score or 0) - (first_submission.syntax_score or 0),
                'complexity_improvement': (first_submission.code_complexity or 0) - (latest_submission.code_complexity or 0),
                'attempts_count': len(submissions),
                'finally_solved': latest_submission.is_correct
            }
        else:
            improvement = {
                'syntax_score_improvement': 0,
                'complexity_improvement': 0,
                'attempts_count': len(submissions),
                'finally_solved': submissions[0].is_correct if submissions else False
            }
        
        return {
            'exercise': {
                'exercise_id': exercise.exercise_id,
                'problem_statement': exercise.problem_statement
            },
            'submission_history': submission_history,
            'improvement_analysis': improvement
        }, 200
        
    except Exception as e:
        return {'error': f'获取练习分析失败: {str(e)}'}, 500

@analytics_bp.route('/research/feedback_effectiveness', methods=['GET'])
@jwt_required()
def get_feedback_effectiveness_analysis() -> Tuple[Dict, int]:
    """获取反馈效果研究分析数据 - 论文第8章核心数据源"""
    user_id = int(get_jwt_identity())
    
    try:
        # 验证管理员权限（研究数据敏感，限制访问）
        user = User.query.get(user_id)
        if not user or not user.is_admin():
            return {'error': '只有管理员可以访问研究分析数据'}, 403
        
        # 核心查询：按反馈类型和错误类型分组的效果统计
        from sqlalchemy import func
        effectiveness_stats = db.session.query(
            FeedbackEffectiveness.feedback_id,
            FeedbackInstance.feedback_type,
            FeedbackEffectiveness.triggering_error_type,
            func.avg(FeedbackEffectiveness.time_to_success_sec).label('avg_success_time'),
            func.avg(FeedbackEffectiveness.subsequent_attempts).label('avg_attempts'),
            func.avg(FeedbackEffectiveness.helpfulness_rating).label('avg_helpfulness'),
            func.count(FeedbackEffectiveness.effectiveness_id).label('sample_size'),
            func.sum(func.cast(FeedbackEffectiveness.did_succeed, db.Integer)).label('success_count')
        ).join(
            FeedbackInstance, FeedbackEffectiveness.feedback_id == FeedbackInstance.feedback_id
        ).group_by(
            FeedbackInstance.feedback_type, 
            FeedbackEffectiveness.triggering_error_type
        ).all()
        
        # 数据转换为前端友好格式
        research_matrix = {}
        feedback_types = set()
        error_types = set()
        
        for stat in effectiveness_stats:
            feedback_type = stat.feedback_type.value
            error_type = stat.triggering_error_type.value if stat.triggering_error_type else 'no_error'
            
            feedback_types.add(feedback_type)
            error_types.add(error_type)
            
            if feedback_type not in research_matrix:
                research_matrix[feedback_type] = {}
            
            research_matrix[feedback_type][error_type] = {
                'avg_success_time': float(stat.avg_success_time) if stat.avg_success_time else 0,
                'avg_attempts': float(stat.avg_attempts) if stat.avg_attempts else 0,
                'avg_helpfulness': float(stat.avg_helpfulness) if stat.avg_helpfulness else 0,
                'sample_size': stat.sample_size,
                'success_rate': (stat.success_count / stat.sample_size * 100) if stat.sample_size > 0 else 0
            }
        
        # 总体统计
        total_feedback_instances = FeedbackInstance.query.count()
        total_effectiveness_records = FeedbackEffectiveness.query.count()
        total_successful_cases = FeedbackEffectiveness.query.filter_by(did_succeed=True).count()
        
        # 如果没有真实数据，或者数据过于稀少，提供示例数据用于演示
        # 优先显示真实数据，即使类型单一
        use_demo_data = (
            total_effectiveness_records == 0 or 
            (len(feedback_types) == 0 and len(error_types) == 0) or
            (total_effectiveness_records < 3 and len(feedback_types) < 2 and len(error_types) < 2)
        )
        
        if use_demo_data:
            research_matrix = {
                'judge0_structural': {
                    'syntax_error': {'avg_success_time': 45.2, 'avg_attempts': 2.1, 'avg_helpfulness': 4.2, 'sample_size': 15, 'success_rate': 87.5},
                    'name_error': {'avg_success_time': 32.8, 'avg_attempts': 1.8, 'avg_helpfulness': 4.0, 'sample_size': 12, 'success_rate': 92.3},
                    'type_error': {'avg_success_time': 38.5, 'avg_attempts': 2.0, 'avg_helpfulness': 3.9, 'sample_size': 10, 'success_rate': 85.0},
                    'logic_error': {'avg_success_time': 125.6, 'avg_attempts': 3.8, 'avg_helpfulness': 3.2, 'sample_size': 8, 'success_rate': 62.5}
                },
                'openai_educational': {
                    'syntax_error': {'avg_success_time': 52.1, 'avg_attempts': 2.3, 'avg_helpfulness': 4.6, 'sample_size': 18, 'success_rate': 89.2},
                    'name_error': {'avg_success_time': 28.9, 'avg_attempts': 1.6, 'avg_helpfulness': 4.5, 'sample_size': 14, 'success_rate': 95.1},
                    'type_error': {'avg_success_time': 35.2, 'avg_attempts': 1.9, 'avg_helpfulness': 4.3, 'sample_size': 13, 'success_rate': 88.7},
                    'logic_error': {'avg_success_time': 98.4, 'avg_attempts': 3.2, 'avg_helpfulness': 4.1, 'sample_size': 11, 'success_rate': 78.9}
                },
                'deepseek_educational': {
                    'syntax_error': {'avg_success_time': 48.7, 'avg_attempts': 2.0, 'avg_helpfulness': 4.4, 'sample_size': 16, 'success_rate': 91.2},
                    'name_error': {'avg_success_time': 30.5, 'avg_attempts': 1.7, 'avg_helpfulness': 4.2, 'sample_size': 13, 'success_rate': 93.8},
                    'type_error': {'avg_success_time': 42.1, 'avg_attempts': 2.2, 'avg_helpfulness': 4.0, 'sample_size': 11, 'success_rate': 84.6},
                    'logic_error': {'avg_success_time': 110.8, 'avg_attempts': 3.5, 'avg_helpfulness': 3.8, 'sample_size': 9, 'success_rate': 71.4}
                },
                'local_rules': {
                    'syntax_error': {'avg_success_time': 65.3, 'avg_attempts': 2.8, 'avg_helpfulness': 3.5, 'sample_size': 12, 'success_rate': 78.9},
                    'name_error': {'avg_success_time': 41.2, 'avg_attempts': 2.1, 'avg_helpfulness': 3.8, 'sample_size': 10, 'success_rate': 85.2},
                    'type_error': {'avg_success_time': 58.9, 'avg_attempts': 2.6, 'avg_helpfulness': 3.4, 'sample_size': 8, 'success_rate': 76.3},
                    'logic_error': {'avg_success_time': 142.7, 'avg_attempts': 4.2, 'avg_helpfulness': 2.9, 'sample_size': 6, 'success_rate': 58.1}
                }
            }
            feedback_types = ['judge0_structural', 'openai_educational', 'deepseek_educational', 'local_rules']
            error_types = ['syntax_error', 'name_error', 'type_error', 'logic_error']
            
            return {
                'research_matrix': research_matrix,
                'feedback_types': feedback_types,
                'error_types': error_types,
                'overview': {
                    'total_feedback_instances': 156,
                    'total_effectiveness_records': 142,
                    'total_successful_cases': 118,
                    'overall_success_rate': 83.1
                },
                'academic_insights': {
                    'research_question': 'Comparative effectiveness of AI-driven feedback models in programming education',
                    'data_collection_period': 'Demo data for MSc Computer Science project presentation',
                    'methodology': 'Simulated quantitative analysis of feedback effectiveness tracking data'
                },
                'demo_note': 'This is demonstration data for academic presentation. Real data will be collected during system usage.'
            }, 200
        
        return {
            'research_matrix': research_matrix,
            'feedback_types': list(feedback_types),
            'error_types': list(error_types),
            'overview': {
                'total_feedback_instances': total_feedback_instances,
                'total_effectiveness_records': total_effectiveness_records,
                'total_successful_cases': total_successful_cases,
                'overall_success_rate': round((total_successful_cases / total_effectiveness_records * 100) if total_effectiveness_records > 0 else 0, 2)
            },
            'academic_insights': {
                'research_question': 'Comparative effectiveness of AI-driven feedback models in programming education',
                'data_collection_period': 'Ongoing since system deployment',
                'methodology': 'Quantitative analysis of feedback effectiveness tracking data'
            }
        }, 200
        
    except Exception as e:
        return {'error': f'获取研究分析数据失败: {str(e)}'}, 500

@analytics_bp.route('/admin/platform_analysis', methods=['GET'])
@jwt_required()
def get_platform_analysis() -> Tuple[Dict, int]:
    """获取平台整体分析数据（管理员功能）"""
    user_id = int(get_jwt_identity())
    
    try:
        # 验证管理员权限
        user = User.query.get(user_id)
        if not user or not user.is_admin():
            return {'error': '权限不足'}, 403
        
        # 获取所有学习者的统计数据
        learners = User.query.filter_by(role=UserRole.STUDENT).all()
        
        platform_stats = {
            'total_learners': len(learners),
            'active_learners': 0,
            'avg_success_rate': 0,
            'avg_code_quality': 0,
            'common_errors': {},
            'exercise_difficulty': {}
        }
        
        total_success_rates = []
        total_quality_scores = []
        all_error_types = {}
        all_submissions_by_exercise = {}  # 收集所有提交按练习分组
        
        for learner in learners:
            learner_submissions = Submission.query.filter_by(user_id=learner.user_id).all()
            
            if learner_submissions:
                platform_stats['active_learners'] += 1
                
                # 计算学习者成功率
                correct_count = len([s for s in learner_submissions if s.is_correct])
                success_rate = (correct_count / len(learner_submissions)) * 100
                total_success_rates.append(success_rate)
                
                # 计算平均代码质量
                quality_scores = [s.syntax_score for s in learner_submissions if s.syntax_score is not None]
                if quality_scores:
                    avg_quality = sum(quality_scores) / len(quality_scores)
                    total_quality_scores.append(avg_quality)
                
                # 统计错误类型并按练习分组提交记录
                for submission in learner_submissions:
                    if not submission.is_correct and submission.error_type:
                        error_type = submission.error_type.value
                        all_error_types[error_type] = all_error_types.get(error_type, 0) + 1
                    
                    # 按练习ID分组提交记录
                    exercise_id = submission.exercise_id
                    if exercise_id not in all_submissions_by_exercise:
                        all_submissions_by_exercise[exercise_id] = []
                    all_submissions_by_exercise[exercise_id].append(submission)
        
        # 计算平均值
        platform_stats['avg_success_rate'] = round(sum(total_success_rates) / len(total_success_rates), 2) if total_success_rates else 0
        platform_stats['avg_code_quality'] = round(sum(total_quality_scores) / len(total_quality_scores), 2) if total_quality_scores else 0
        platform_stats['common_errors'] = dict(sorted(all_error_types.items(), key=lambda x: x[1], reverse=True)[:5])
        
        # 分析练习难度 - 使用已收集的数据，避免重复查询
        exercises = Exercise.query.all()
        for exercise in exercises:
            exercise_submissions = all_submissions_by_exercise.get(exercise.exercise_id, [])
            if exercise_submissions:
                correct_count = len([s for s in exercise_submissions if s.is_correct])
                difficulty_score = (correct_count / len(exercise_submissions)) * 100
                platform_stats['exercise_difficulty'][exercise.exercise_id] = {
                    'title': exercise.problem_statement[:50] + '...' if len(exercise.problem_statement) > 50 else exercise.problem_statement,
                    'success_rate': round(difficulty_score, 2),
                    'total_attempts': len(exercise_submissions)
                }
        
        return platform_stats, 200
        
    except Exception as e:
        return {'error': f'获取平台分析失败: {str(e)}'}, 500